import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/buyers/buyer_view_store/buyer_view_store_bloc.dart';
import 'package:swadesic/features/buyers/buyer_view_store/store_details_screen/store_details_screen.dart';
import 'package:swadesic/features/buyers/buyer_view_store/store_products/store_products.dart';
import 'package:swadesic/features/common_buyer_seller_screen/invalid/invalid_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/share_store_bottom_sheet_screen/share_store_bottom_sheet_screen.dart';
import 'package:swadesic/features/post/post_screen.dart';
import 'package:swadesic/features/repost/repost_screen.dart';
import 'package:swadesic/features/seller/seller_store/store_dashboard/store_activate_and_open_card/store_activate_and_open_card.dart';
import 'package:swadesic/features/seller/seller_store/store_dashboard/store_dashboard_screen.dart';
import 'package:swadesic/features/widgets/verified_badge.dart';
import 'package:swadesic/services/app_analytics/app_analytics.dart';
import 'package:swadesic/services/app_link_services/page_url_service.dart';

// import 'package:swadesic/model/buyer_search_response/store_list_response.dart' as store_list_response;
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/app_tool_tip/app_tool_tip.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/util/auto_hide_navigation_service.dart';

// region Buyer Store Screen
class BuyerViewStoreScreen extends StatefulWidget {
  final String? storeReference;
  final bool? isStoreOwnerView;
  final bool isFromBottomNavigation;
  final bool isLeadingVisible;
  final int initialTapIndex;

  // final String storeHandle;
  const BuyerViewStoreScreen({
    Key? key,
    this.storeReference,
    this.isStoreOwnerView = false,
    this.isFromBottomNavigation = false,
    this.isLeadingVisible = true,
    this.initialTapIndex = 0,
  }) : super(key: key);

  @override
  _BuyerViewStoreScreenState createState() => _BuyerViewStoreScreenState();
}
// endregion

class _BuyerViewStoreScreenState extends State<BuyerViewStoreScreen>
    with
        TickerProviderStateMixin,
        AutoHideNavigationMixin<BuyerViewStoreScreen> {
  //region Tab controller
  late TabController tabController = TabController(
      length: (AppConstants.appData.isStoreView! &&
              widget.storeReference == AppConstants.appData.storeReference!)
          ? 5
          : 4,
      vsync: this,
      initialIndex: widget.initialTapIndex);

  //endregion
  // region Bloc
  late BuyerViewStoreBloc buyerViewStoreBloc;

  // endregion

  // region Init
  @override
  void initState() {
    buyerViewStoreBloc =
        BuyerViewStoreBloc(context, widget.storeReference, tabController);
    buyerViewStoreBloc.init();
    //Add store info in analytics
    AppAnalytics.viewEventLog(reference: widget.storeReference!);

    ///If from bottom navigation then save this context as selected tab context
    if (widget.isFromBottomNavigation) {
      AppConstants.currentSelectedTabContext = context;
    }
    AppConstants.isBottomNavigationMounted.value = true;

    // Initialize auto-hide navigation only if not from bottom navigation
    // (to avoid conflicts with main navigation)
    if (!widget.isFromBottomNavigation) {
      enableAutoHideNavigation();
      attachScrollControllerToAutoHide(buyerViewStoreBloc.scrollController);
    }

    super.initState();
  }

  // endregion

  //region Dispose
  @override
  void dispose() {
    // Detach scroll controller from auto-hide service if it was attached
    if (!widget.isFromBottomNavigation) {
      detachScrollControllerFromAutoHide(buyerViewStoreBloc.scrollController);
      disableAutoHideNavigation();
    }

    PaintingBinding.instance.imageCache.clear();
    buyerViewStoreBloc.dispose();
    imageCache.clear();
    super.dispose();
  }

  //endregion

  // region build
  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 2,
      child: Scaffold(
        backgroundColor: AppColors.appWhite,
        appBar: appBar(),
        body: StreamBuilder<BuyerViewStoreState>(
            stream: buyerViewStoreBloc.storeDetailCtrl.stream,
            initialData: BuyerViewStoreState.Loading,
            builder: (context, snapshot) {
              //print("State of store is : ${snapshot.data}");
              //Success
              if (snapshot.data == BuyerViewStoreState.Success) {
                // return ListView(
                //   shrinkWrap: true,
                //
                //   controller: buyerViewStoreBloc.scrollController,
                //  children: [
                //    storeDetail(),
                //    ///Tab bar
                //    tabAppBar(),
                //    sellerOwnAndOtherStoreProductView(),
                //  ],
                // );

                return NestedScrollView(
                  controller: buyerViewStoreBloc.scrollController,
                  // physics: const NeverScrollableScrollPhysics(),
                  headerSliverBuilder:
                      (BuildContext context, bool innerBoxIsScrolled) {
                    return <Widget>[
                      ///Store detail
                      SliverToBoxAdapter(
                        child: storeDetail(),
                      ),

                      ///Tab bar
                      tabAppBar(),
                    ];
                  },

                  ///Tab view
                  body: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      //Visible only if seller view and seller viewing own store

                      // //Seller view
                      // AppConstants.appData.isStoreView!
                      //     ? Visibility(
                      //         visible: (widget.storeReference ==
                      //             AppConstants.appData.storeReference),
                      //         child: StoreActivateAndOpenCard(
                      //             storeReference:
                      //                 AppConstants.appData.storeReference!))
                      //     : const SizedBox(),
                      Expanded(child: sellerOwnAndOtherStoreProductView()),
                    ],
                  ),
                );
              }
              //Loading
              if (snapshot.data == BuyerViewStoreState.Loading) {
                return Container(
                  alignment: Alignment.center,
                  margin: const EdgeInsets.symmetric(vertical: 100),
                  width: double.infinity,
                  child: AppCommonWidgets.appCircularProgress(),
                );
              }
              //Failed
              if (snapshot.data == BuyerViewStoreState.Failed) {
                return AppCommonWidgets.errorWidget(
                    errorMessage: AppStrings.unableToLoadStoreInfo,
                    onTap: () {
                      buyerViewStoreBloc.getSingleStoreInfo();
                    });
              }
              //Inactive
              if (snapshot.data == BuyerViewStoreState.InActive) {
                return const InvalidScreen();
              }
              //Deleted
              if (snapshot.data == BuyerViewStoreState.Deleted) {
                return const InvalidScreen();
              }

              //Test store did not created by logged in user
              //NotOwnedTestStore
              if (snapshot.data == BuyerViewStoreState.NotOwnedTestStore) {
                return const InvalidScreen();
              }
              return const SizedBox();
            }),
      ),
    );
  }

  // endregion

  //region AppBar
  AppBar appBar() {
    return AppCommonWidgets.mainAppBar(
        context: context,
        toolbarHeight: 40,
        isTitleVisible: true,
        onTapLeading: () {
          widget.isFromBottomNavigation
              ? buyerViewStoreBloc.goToFirstBottomNavigation()
              : Navigator.pop(context);
        },
        isLeadingVisible: widget.isLeadingVisible,
        isCustomTitle: true,
        customTitleWidget: Padding(
          padding: EdgeInsets.only(left: widget.isLeadingVisible ? 0 : 20),
          child: StreamBuilder<BuyerViewStoreState>(
              stream: buyerViewStoreBloc.storeDetailCtrl.stream,
              initialData: BuyerViewStoreState.Loading,
              builder: (context, snapshot) {
                if (snapshot.data == BuyerViewStoreState.Success) {
                  return Row(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      AppCommonWidgets.appBarTitleText(
                          text: buyerViewStoreBloc
                              .singleStoreInfoResponse.data!.storehandle!
                              .toLowerCase()),
                      // Visibility(
                      //   visible: buyerViewStoreBloc.singleStoreInfoResponse.data!.isVerificationCompleted!,
                      //   child: AppToolTip(
                      //     message: AppStrings.verifiedBySwadesic,
                      //     toolTipWidget: Container(
                      //         alignment: Alignment.centerLeft,
                      //         margin: const EdgeInsets.only(left: 10),
                      //         height: 24,width: 24,
                      //         child: Image.asset(AppImages.storeVerified,height: 24,width: 24,)),
                      //   ),
                      // )
                      Visibility(
                        visible: buyerViewStoreBloc.singleStoreInfoResponse
                                .data!.subscriptionType ==
                            SubscriptionTypeEnum.PREMIUM.name,
                        child: VerifiedBadge(
                          margin: const EdgeInsets.only(left: 5),
                          height: 18,
                          width: 18,
                          subscriptionType: SubscriptionTypeEnum.PREMIUM.name,
                        ),
                        // child: Container(
                        //     padding: const EdgeInsets.symmetric(horizontal: 10),
                        //     child: SvgPicture.asset(AppImages.verified,height: 25,width: 25,)),
                      )
                    ],
                  );
                }
                return AppCommonWidgets.appBarTitleText(text: "");
              }),
        ),
        isDefaultMenuVisible: false,
        customMenuButton: myMenuButton(),
        isCustomMenuVisible: widget.isStoreOwnerView! ? false : true,
        isCartVisible: AppConstants.appData.storeReference != null
            ? false
            : widget.isStoreOwnerView!
                ? false
                : true,
        isMembershipVisible: AppConstants.appData.storeReference != null
            ? false
            : widget.isStoreOwnerView!
                ? false
                : true,
        isDropdownVisible: widget.isStoreOwnerView! ? true : false,
        onTapDropdown: () {
          buyerViewStoreBloc.goToSellerAccountScreen();
        },
        onTapReport: () {
          buyerViewStoreBloc.goToReportScreen();
        });
  }

  //endregion

  //region Menu button
  Widget myMenuButton() {
    return PopupMenuButton(
      // shadowColor: Colors.transparent,
      // add icon, by default "3 dot" icon
      // icon: Icon(Icons.book)
      padding: EdgeInsets.zero,
      icon: Icon(Icons.more_vert, size: 20),
      // icon: SvgPicture.asset(AppImages.drawerIcon,
      //     color: AppColors.appBlack, height: 24, width: 24),
      itemBuilder: (context) {
        return [
          ///Report
          PopupMenuItem<int>(
            height: 0,
            value: 0,
            onTap: () async {
              buyerViewStoreBloc.goToReportScreen();
            },
            padding: EdgeInsets.zero,
            child: SizedBox(
              width: 150,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                      padding: const EdgeInsets.all(10),
                      child:
                          AppCommonWidgets.menuText(text: AppStrings.report)),
                  divider()
                ],
              ),
            ),
          ),

          ///Share
          PopupMenuItem<int>(
            height: 0,
            value: 0,
            onTap: () async {
              await Future.delayed(Duration.zero);
              //print("hello");
              context.mounted
                  ? CommonMethods.appMinimumBottomSheets(
                      screen: ShareInSocialBottomSheetScreen(
                          storeInfo:
                              buyerViewStoreBloc.singleStoreInfoResponse.data!),
                      context: context,
                    )
                  : null;
            },
            padding: EdgeInsets.zero,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Container(
                    padding: const EdgeInsets.all(10),
                    child:
                        AppCommonWidgets.menuText(text: AppStrings.shareStore)

                    // child: appText(AppStrings.shareProfile,
                    //     fontFamily: AppConstants.rRegular,
                    //     fontSize: 14,
                    //     fontWeight: FontWeight.w700
                    // )

                    ),
                // divider()
              ],
            ),
          ),
        ];
      },
    );
  }

  //endregion

  ///Store detail
  //region Store detail
  Widget storeDetail() {
    // Set page URL for web app navigation
    final storeData = buyerViewStoreBloc.singleStoreInfoResponse.data!;
    PageUrlService.setPageUrlAfterBuild(
        '/${storeData.storehandle}', 'Store Profile');

    return RefreshIndicator(
      color: AppColors.brandBlack,
      onRefresh: () async {
        buyerViewStoreBloc.widgetKey = UniqueKey();

        // Refresh store info and products
        await buyerViewStoreBloc.getSingleStoreInfo();
        await buyerViewStoreBloc.refreshStoreProducts();
      },
      child: ListView(
        shrinkWrap: true,
        children: [
          StoreDetailsScreen(
              storeInfo: buyerViewStoreBloc.singleStoreInfoResponse.data!,
              key: buyerViewStoreBloc.widgetKey),
        ],
      ),
    );
  }

// endregion

  ///Tab bars
  //region Tab bar
  Widget tabAppBar() {
    return SliverAppBar(
      expandedHeight: kToolbarHeight * 0.5,
      automaticallyImplyLeading: false,
      leadingWidth: 0.0,
      titleSpacing: 0,
      floating: false,
      pinned: true,
      backgroundColor: AppColors.appWhite,
      title: StreamBuilder<bool>(
          stream: buyerViewStoreBloc.tabRefreshCtrl.stream,
          builder: (context, snapshot) {
            return
                //Admin
                AppConstants.appData.isStoreView! &&
                        widget.storeReference ==
                            AppConstants.appData.storeReference!
                    ? adminStoreTabBar()
                    :
                    //Other store
                    otherStoreTabBar();
          }),
    );
  }

  //endregion

  ///Seller own store adn other product view
  //region Seller own store and other product view
  Widget sellerOwnAndOtherStoreProductView() {
    return AppConstants.appData.isStoreView! &&
            widget.storeReference == AppConstants.appData.storeReference!
        ? storeAdminTabView()
        : otherStoreTabView();
  }
//endregion

  ///Store admin and other store Tab Bar
  //region Admin store Tab bar
  Widget adminStoreTabBar() {
    return TabBar(
      controller: buyerViewStoreBloc.tabController,
      indicatorColor: AppColors.appBlack,
      indicatorWeight: 1,
      tabs: [
        //Product
        Tab(
          child: SvgPicture.asset(
            buyerViewStoreBloc.tabController.index == 0
                ? AppImages.tabProductActive
                : AppImages.tabProductInactive,
            color: buyerViewStoreBloc.tabController.index == 0
                ? AppColors.appBlack
                : AppColors.borderColor1,
          ),
        ),
        //Post
        Tab(
          child: SvgPicture.asset(
            AppImages.userFeedTab,
            color: buyerViewStoreBloc.tabController.index == 1
                ? AppColors.appBlack
                : AppColors.borderColor1,
          ),
        ),
        //Repost
        Tab(
          child: SvgPicture.asset(
            AppImages.repostForTab,
            color: buyerViewStoreBloc.tabController.index == 2
                ? AppColors.appBlack
                : AppColors.borderColor1,
          ),
        ),
        //Tagged content
        Tab(
          child: SvgPicture.asset(
            AppImages.taggedContentTab1,
            color: buyerViewStoreBloc.tabController.index == 3
                ? AppColors.appBlack
                : AppColors.borderColor1,
          ),
        ),
        //Dashboard
        Tab(
          child: SvgPicture.asset(
            AppImages.loggedInUserAccessTab,
            color: buyerViewStoreBloc.tabController.index == 4
                ? AppColors.appBlack
                : AppColors.borderColor1,
          ),
        ),
      ],
    );
  }
  //endregion

  //region Other store Tab bar
  Widget otherStoreTabBar() {
    return TabBar(
      controller: buyerViewStoreBloc.tabController,
      indicatorColor: AppColors.appBlack,
      indicatorWeight: 1,
      tabs: [
        //Product
        Tab(
          child: SvgPicture.asset(
            buyerViewStoreBloc.tabController.index == 0
                ? AppImages.tabProductActive
                : AppImages.tabProductInactive,
            color: buyerViewStoreBloc.tabController.index == 0
                ? AppColors.appBlack
                : AppColors.borderColor1,
          ),
        ),
        //Post
        Tab(
          child: SvgPicture.asset(
            AppImages.userFeedTab,
            color: buyerViewStoreBloc.tabController.index == 1
                ? AppColors.appBlack
                : AppColors.borderColor1,
          ),
        ),
        //Repost
        Tab(
          child: SvgPicture.asset(
            AppImages.repostForTab,
            color: buyerViewStoreBloc.tabController.index == 2
                ? AppColors.appBlack
                : AppColors.borderColor1,
          ),
        ),

        //Tagged
        Tab(
          child: SvgPicture.asset(
            AppImages.taggedContentTab1,
            color: buyerViewStoreBloc.tabController.index == 3
                ? AppColors.appBlack
                : AppColors.borderColor1,
          ),
        ),
      ],
    );
  }
  //endregion

  ///Store admin and other store Tab view

//region Store admin Tab view
  Widget storeAdminTabView() {
    return TabBarView(
      controller: buyerViewStoreBloc.tabController,
      children: [
        //Products
        StoreProducts(
          key: buyerViewStoreBloc.storeProductsKey,
          storeReference: buyerViewStoreBloc.storeReference!,
          storeInfo: buyerViewStoreBloc.singleStoreInfoResponse.data!,
        ),
        //Posts
        PostScreen(
            key: const PageStorageKey('admin_posts_tab'),
            storeOrUserReference: buyerViewStoreBloc.storeReference!,
            previousScrollController: buyerViewStoreBloc.scrollController),
        //Repost
        PostScreen(
            key: const PageStorageKey('admin_repost_tab'),
            storeOrUserReference: buyerViewStoreBloc.storeReference!,
            previousScrollController: buyerViewStoreBloc.scrollController,
            isRepost: true),

        //tagged content
        PostScreen(
            key: const PageStorageKey('admin_tagged_tab'),
            storeOrUserReference: buyerViewStoreBloc.storeReference!,
            previousScrollController: buyerViewStoreBloc.scrollController,
            isTagged: true),

        //Dashboard
        StoreDashBoardScreen(
            storeReference: buyerViewStoreBloc
                .singleStoreInfoResponse.data!.storeReference!),
      ],
    );
  }
//endregion

//region Other store Tab view
  Widget otherStoreTabView() {
    return TabBarView(
      controller: buyerViewStoreBloc.tabController,
      children: [
        //Products
        StoreProducts(
          key: buyerViewStoreBloc.storeProductsKey,
          storeReference: buyerViewStoreBloc.storeReference!,
          storeInfo: buyerViewStoreBloc.singleStoreInfoResponse.data!,
        ),
        //Posts
        PostScreen(
            key: const PageStorageKey('other_posts_tab'),
            storeOrUserReference: buyerViewStoreBloc.storeReference!,
            previousScrollController: buyerViewStoreBloc.scrollController),
        //Repost
        PostScreen(
            key: const PageStorageKey('other_repost_tab'),
            storeOrUserReference: buyerViewStoreBloc.storeReference!,
            previousScrollController: buyerViewStoreBloc.scrollController,
            isRepost: true),

        //tagged content
        PostScreen(
            key: const PageStorageKey('other_tagged_tab'),
            storeOrUserReference: buyerViewStoreBloc.storeReference!,
            previousScrollController: buyerViewStoreBloc.scrollController,
            isTagged: true),
      ],
    );
  }
//endregion
}
