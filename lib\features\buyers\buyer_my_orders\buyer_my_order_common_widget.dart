import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/buyer_sub_order/buyer_sub_order_bloc.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/buyer_view_single_product/buyer_view_single_product_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/cancelled_or_returned_products/cancelled_or_returned_products_screen.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/model/order_response/sub_order.dart';

//region Product Detail
Widget buyerSubOrderCard({
  required BuildContext context,
  required SubOrder subOrder,
  bool isConfirmedVisible = false,
  bool isPaidOnVisible = false,
  bool isDeliveryReturnOnVisible = false,
  bool isReturnWindowLastDateVisible = false,
  bool isRequestOnTrackPackageVisible = false,
  bool isOnlyReturnOnVisible = false,
  bool isCancelledOnVisible = false,
}) {
  return GestureDetector(
    onTap: () {
      var screen = BuyerViewSingleProductScreen(
        productReference: subOrder.productReference,
        productVersion: subOrder.productVersion,
        subOrderNumber: subOrder.suborderNumber,
      );
      // var screen = const MyOrdersScreen();
      var route = MaterialPageRoute(builder: (context) => screen);
      Navigator.push(context, route);
    },
    child: Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        //Confirmed date
        Visibility(
          visible: isConfirmedVisible,
          child: Container(
            margin: const EdgeInsets.only(bottom: 10),
            child: Text(
              "Confirmed on\n22-09-22",
              maxLines: 2,
              overflow: TextOverflow.visible,
              textAlign: TextAlign.left,
              style: TextStyle(
                fontFamily: "LatoSemiBold",
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: AppColors.appBlack,
              ),
            ),
          ),
        ),
        //Paid on
        Visibility(
          visible: isPaidOnVisible,
          child: Container(
            margin: const EdgeInsets.only(bottom: 10),
            child: Text(
              "Paid on\n24-01-2021",
              maxLines: 2,
              overflow: TextOverflow.visible,
              textAlign: TextAlign.left,
              style: TextStyle(
                fontFamily: "LatoSemiBold",
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: AppColors.appBlack,
              ),
            ),
          ),
        ),
        //Delivery Successfully at and Return requested on
        Visibility(
          visible: isDeliveryReturnOnVisible,
          child: Container(
            margin: const EdgeInsets.only(bottom: 10),
            child: Row(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  "Delivered successfully at \n24-01-2021, 11:25 AM",
                  maxLines: 2,
                  overflow: TextOverflow.visible,
                  textAlign: TextAlign.left,
                  style: TextStyle(
                    fontFamily: "LatoSemiBold",
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: AppColors.appBlack,
                  ),
                ),
                Text(
                  "Return requested on\n24-01-2021",
                  maxLines: 2,
                  overflow: TextOverflow.visible,
                  textAlign: TextAlign.left,
                  style: TextStyle(
                    fontFamily: "LatoSemiBold",
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: AppColors.appBlack,
                  ),
                ),
              ],
            ),
          ),
        ),
        //Return window last date
        Visibility(
          visible: isReturnWindowLastDateVisible,
          child: Container(
            margin: const EdgeInsets.only(bottom: 10),
            child: Row(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  "Return window last date: 14-02-2021",
                  maxLines: 2,
                  overflow: TextOverflow.visible,
                  textAlign: TextAlign.left,
                  style: TextStyle(
                    fontFamily: "LatoSemiBold",
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: AppColors.appBlack,
                  ),
                ),
                SvgPicture.asset(
                  AppImages.exclamation,
                  color: AppColors.darkGray,
                )
              ],
            ),
          ),
        ),
        //Return request on and track package
        Visibility(
          visible: isRequestOnTrackPackageVisible,
          child: Container(
            margin: const EdgeInsets.only(bottom: 10),
            child: Row(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  "Return requested on\n24-01-2021",
                  maxLines: 2,
                  overflow: TextOverflow.visible,
                  textAlign: TextAlign.left,
                  style: TextStyle(
                    fontFamily: "LatoSemiBold",
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: AppColors.appBlack,
                  ),
                ),
                Container(
                  padding:
                      const EdgeInsets.symmetric(vertical: 10, horizontal: 10),
                  decoration: BoxDecoration(
                    color: AppColors.brandBlack,
                    borderRadius: BorderRadius.all(Radius.circular(10)),
                  ),
                  child: Center(
                    child: Text(
                      "Track the package",
                      style: TextStyle(
                          fontFamily: "LatoBold",
                          fontWeight: FontWeight.w700,
                          fontSize: 14,
                          color: AppColors.appWhite),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
        //Only return
        Visibility(
          visible: isOnlyReturnOnVisible,
          child: Container(
            margin: const EdgeInsets.only(bottom: 10),
            child: Text(
              "Returned on 24-01-2021",
              maxLines: 2,
              overflow: TextOverflow.visible,
              textAlign: TextAlign.left,
              style: TextStyle(
                fontFamily: "LatoSemiBold",
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: AppColors.appBlack,
              ),
            ),
          ),
        ),
        //Cancelled date
        Visibility(
          visible: isCancelledOnVisible,
          child: Container(
            margin: const EdgeInsets.only(bottom: 10),
            child: Text(
              "Cancelled on\n${subOrder.cancelledDate}",
              maxLines: 2,
              overflow: TextOverflow.visible,
              textAlign: TextAlign.left,
              style: TextStyle(
                fontFamily: "LatoSemiBold",
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: AppColors.appBlack,
              ),
            ),
          ),
        ),

        Container(
          margin: const EdgeInsets.only(bottom: 10),
          height: 70,
          child: Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    //Product name
                    Text(
                      subOrder.productName!,
                      maxLines: 2,
                      overflow: TextOverflow.visible,
                      textAlign: TextAlign.left,
                      style: TextStyle(
                        fontFamily: "LatoSemiBold",
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: AppColors.darkGray,
                      ),
                    ),
                    Expanded(child: verticalSizedBox(5)),
                    //Quantity
                    Text(
                      "quantity : ${subOrder.productQuantity!}",
                      maxLines: 1,
                      textAlign: TextAlign.left,
                      style: TextStyle(
                        fontFamily: "LatoSemiBold",
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: AppColors.appBlack,
                      ),
                    ),
                    //Product Price
                    Text(
                      "₹ ${subOrder.sellingPrice!}",
                      maxLines: 1,
                      textAlign: TextAlign.left,
                      style: TextStyle(
                        fontFamily: "LatoSemiBold",
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: AppColors.appBlack,
                      ),
                    ),
                  ],
                ),
              ),
              //Product image
              ClipRRect(
                  borderRadius: const BorderRadius.all(Radius.circular(10)),
                  child: Container(
                      color: AppColors.textFieldFill1,
                      height: 70,
                      width: 70,
                      child: extendedImage(
                        "${subOrder.displayProductImage}",
                        context,
                        100,
                        100,
                        cache: true,
                      )))
            ],
          ),
        ),
      ],
    ),
  );
}

//endregion

//region Tip
Widget tip(String message) {
  return Container(
    color: AppColors.appWhite,
    child: Align(
      alignment: Alignment.centerLeft,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 10),
        child: Container(
          decoration: BoxDecoration(
              //color: Colors.red,
              border: Border.all(width: 1.0, color: AppColors.lightStroke),
              borderRadius: const BorderRadius.all(Radius.circular(30))),
          padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              SvgPicture.asset(
                AppImages.yellowLightBulb,
                fit: BoxFit.cover,
              ),
              horizontalSizedBox(5),
              Expanded(
                child: Text(
                  message,
                  maxLines: 1,
                  style: TextStyle(
                      fontSize: 12,
                      overflow: TextOverflow.visible,
                      fontFamily: "LatoRegular",
                      fontWeight: FontWeight.w400,
                      color: AppColors.appBlack),
                ),
              ),
              horizontalSizedBox(5),
              SvgPicture.asset(
                AppImages.close,
                fit: BoxFit.cover,
              ),

              // Expanded(child: horizontalSizedBox(5)),
            ],
          ),
        ),
      ),
    ),
  );
}
//endregion

//region Blue button
Widget buyerMyOrderActionButton(
    {onPress, String buttonName = "", Color? colors, Color? textColor}) {
  colors = colors ?? AppColors.inActiveGreen;
  textColor = textColor ?? AppColors.brandBlack;
  return Expanded(
    child: InkWell(
      onTap: () {
        onPress();
      },
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(vertical: 10),
        decoration: BoxDecoration(
          color: colors,
          borderRadius: const BorderRadius.all(Radius.circular(20)),
        ),
        child: Center(
          child: Text(
            buttonName,
            style: TextStyle(
                fontFamily: "LatoBold",
                fontWeight: FontWeight.w700,
                fontSize: 14,
                color: textColor),
          ),
        ),
      ),
    ),
  );
}
//endregion

//region Grey button
Widget buyerMyOrderCancelButton(
    {onPress, String buttonName = "", Color? buttonColor, Color? textColor}) {
  buttonColor = buttonColor ?? AppColors.lightGray;
  textColor = textColor ?? AppColors.appBlack;
  return Expanded(
    child: InkWell(
      onTap: () {
        onPress();
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 13, horizontal: 20),
        decoration: BoxDecoration(
          color: buttonColor,
          borderRadius: const BorderRadius.all(Radius.circular(200)),
        ),
        child: Center(
          child: Text(
            buttonName,
            style: AppTextStyle.button2Bold(textColor: textColor),
            // style: TextStyle(
            //     fontFamily:AppConstants.rRegular,
            //     fontWeight: FontWeight.w600,
            //     fontSize: 14,
            //     color: AppColors.writingColor3),
          ),
        ),
      ),
    ),
  );
}
//endregion

//region Sub Order Tag
Widget subOrderTag(String suborderTag) {
  return Container(
    padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
    decoration: BoxDecoration(
        color: AppColors.textFieldFill1,
        borderRadius: BorderRadius.all(Radius.circular(30))),
    child: appText(suborderTag,
        color: AppColors.appBlack,
        fontWeight: FontWeight.w400,
        fontFamily: AppConstants.rRegular,
        fontSize: 12,
        maxLine: 1),
  );
}
//endregion

//region Buyer bottom sheet sub order details
Widget buyerBottomSheetSubOrderDetails(
    {required SubOrder subOrder,
    required BuildContext context,
    bool isPriceQuantityVisible = false,
    String subOrderStatus = "",
    bool showCheckBox = false}) {
  return Padding(
    padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 25),
    child: Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                showCheckBox
                    ? Stack(
                        alignment: Alignment.center,
                        children: [
                          Container(
                            margin: const EdgeInsets.only(right: 10),
                            height: 25,
                            width: 25,
                            child: Checkbox(
                              value: subOrder.isSelected,
                              activeColor: AppColors.primaryGreen,
                              onChanged: (bool? value) {
                                subOrder.isSelected = !subOrder.isSelected;
                              },
                            ),
                          ),
                          Container(
                            height: 25,
                            width: 25,
                            color: Colors.transparent,
                          )
                        ],
                      )
                    : const SizedBox(),
                Expanded(
                    child: Padding(
                  padding: const EdgeInsets.only(right: 20),
                  child: appText(subOrder.productName!,
                      color: AppColors.writingBlack,
                      fontWeight: FontWeight.w600,
                      fontFamily: AppConstants.rRegular,
                      fontSize: 14,
                      maxLine: 2),
                )),
                ClipRRect(
                  borderRadius: const BorderRadius.all(Radius.circular(6)),
                  child: Container(
                    height: 35,
                    width: 35,
                    decoration: const BoxDecoration(
                        borderRadius: BorderRadius.all(Radius.circular(6))),
                    child: extendedImage(
                      subOrder.displayProductImage!,
                      context,
                      100,
                      100,
                      cache: true,
                    ),
                  ),
                )
              ],
            ),

            ///Price and quantity
            isPriceQuantityVisible
                ? Padding(
                    padding: const EdgeInsets.symmetric(vertical: 10),
                    child:
                        //Delivery fee and price
                        Row(
                      mainAxisSize: MainAxisSize.max,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          "₹${subOrder.sellingPrice} X ${subOrder.productQuantity} = ₹${subOrder.sellingPrice! * subOrder.productQuantity!}",
                          style: AppTextStyle.heading3Regular(
                            textColor: AppColors.writingColor2,
                          ),
                        ),
                        Text(
                          "${AppStrings.df}: ${subOrder.suborderFeeDetails!.productLevelDeliveryFee != 0 ? subOrder.suborderFeeDetails!.productLevelDeliveryFee : "0"}",
                          style: AppTextStyle.heading3Regular(
                            textColor: AppColors.writingColor2,
                          ),
                        ),
                        Text(
                          "${AppStrings.returnText}: ${subOrder.returnDays == 0 ? AppStrings.noReturn : "${subOrder.returnDays} days"}",
                          style: AppTextStyle.heading3Regular(
                            textColor: AppColors.writingColor2,
                          ),
                        )
                      ],
                    ),
                  )
                : verticalSizedBox(10),

            ///Suborder Tag
            subOrderStatus == ""
                ? const SizedBox()
                : subOrderTag(subOrderStatus)
          ],
        ),
      ],
    ),
  );
}

//endregion
class BuyerMyOrderCommonWidgets {
  //region How refund amount is calculated?
  static Widget howRefundAmountCalculated(
      {required List<SubOrder> subOrderList,
      required BuyerSubOrderBloc buyerSubOrderBloc}) {
    return Row(
      children: [
        Expanded(
          child: AppCommonWidgets.activeButton(
              buttonName: AppStrings.howRefundAmountIsCalculated,
              onTap: () {
                var screen = CancelledOrReturnedProductsScreen(
                  subOrderList: subOrderList,
                  order:
                      buyerSubOrderBloc.getSubOrdersResponse.orderList!.first,
                  isSellerView: false,
                );
                var route = MaterialPageRoute(builder: (context) => screen);
                Navigator.push(buyerSubOrderBloc.context, route).then((value) {
                  //getSellerSubOrder();
                  //print(value);
                });
              }),
        ),
      ],
    );
  }
//endregion

  //region Speak with  customer
  static Widget speakWithCustomer(
      {required List<SubOrder> subOrderList,
      required BuyerSubOrderBloc buyerSubOrderBloc}) {
    return InkWell(
      onTap: () {
        var screen = CancelledOrReturnedProductsScreen(
          subOrderList: subOrderList,
          order: buyerSubOrderBloc.getSubOrdersResponse.orderList!.first,
          isSellerView: false,
        );
        var route = MaterialPageRoute(builder: (context) => screen);
        Navigator.push(buyerSubOrderBloc.context, route).then((value) {
          //getSellerSubOrder();
          //print(value);
        });
      },
      child: Container(
        width: double.infinity,
        alignment: Alignment.center,
        padding: const EdgeInsets.symmetric(vertical: 13),
        decoration: BoxDecoration(
          color: AppColors.brandBlack,
          borderRadius: BorderRadius.all(Radius.circular(100)),
        ),
        child: Text(
          AppStrings.speakWithYourCustomer,
          style: AppTextStyle.button2Bold(textColor: AppColors.appWhite),
        ),
      ),
    );
  }
//endregion

//region Buyer my order action button
  static Widget buyerMyOrderActionButton(
      {onPress, String buttonName = "", Color? buttonColor, Color? textColor}) {
    buttonColor = buttonColor ?? AppColors.inActiveGreen;
    textColor = textColor ?? AppColors.brandBlack;
    return Expanded(
      child: InkWell(
        onTap: () {
          onPress();
        },
        child: Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(vertical: 13, horizontal: 20),
          decoration: BoxDecoration(
            color: buttonColor,
            borderRadius: const BorderRadius.all(Radius.circular(100)),
          ),
          child: Center(
            child: Text(buttonName,
                style: AppTextStyle.button2Bold(textColor: AppColors.appWhite)),
          ),
        ),
      ),
    );
  }
//endregion

//region Buyer component bottom sheet sub-title
  static Widget buyerBottomSheetSubTitle({required String title}) {
    return Row(
      children: [
        Expanded(
            child: Text(
          title,
          style: AppTextStyle.subTitle(textColor: AppColors.writingBlack1),
        )),
      ],
    );
  }
//endregion
}
