import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_home/buyer_home_bloc.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/buyer_sub_order/buyer_sub_order_screen.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/buyer_view_single_product/buyer_view_single_product_screen.dart';
import 'package:swadesic/features/buyers/buyer_view_store/buyer_view_store_screen.dart';
import 'package:swadesic/features/buyers/messaging/new_messaging_chat_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/messaging_and_requests/all_messages/message_detail/message_detail_screen.dart';
import 'package:swadesic/features/seller/seller_all_orders/seller_sub_order/seller_sub_order_screen.dart';
import 'package:swadesic/features/user_profile/user_profile_screen.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/store_user_product_navigation/store_user_product_navigation.dart';

class OnOpenAppNotification {
  // late RemoteMessage notification;

  //Constructor
  // OnOpenAppNotification(){
  //
  //   //print("Bottom navigation mounted check");
  //   bottomNavigationMountedCheck(notification: notification);
  //   //Check if the bottom navigation is mounted or not
  //   //If it is mounted then call the navigation method
  //
  // }

  ///1
  //region Bottom navigation mounted check
  void bottomNavigationMountedCheck({required RemoteMessage notification}) {
    //print("On tap notification add listner is called");

    ///Todo un-commnet to test i did it because it was opening3 times
    // AppConstants.isBottomNavigationMounted.addListener(() async{
    //   // await Future.delayed(const Duration(seconds: 5));
    //   //Is bottom sheet mounted called
    //   checkUserOrStoreView(notification: notification);
    //   return;
    // });
    checkUserOrStoreView(notification: notification);
  }
  //endregion

  ///2
  //region Check user or store view
  //If notification is for user and view is in store then switch to user
  void checkUserOrStoreView({required RemoteMessage notification}) async {
    // navigateToScreen(notification: notification);
    // return;
    //1. Notification is for user
    //2. Store view
    if (notification.data["notified_user_or_store"]
                .toString()
                .split("")
                .first ==
            'U' &&
        AppConstants.appData.isStoreView!) {
      // Show switching message
      CommonMethods.toastMessage(
        "Switching to user account",
        AppConstants.userStoreCommonBottomNavigationContext,
        toastShowTimer: 3,
      );

      // Switch to buyer account
      await CommonMethods.switchToBuyer(
          context: AppConstants.userStoreCommonBottomNavigationContext);

      // Add a small delay to ensure the navigation is complete
      await Future.delayed(const Duration(milliseconds: 500));

      //Navigation - immediate redirection after account switch
      navigateToScreen(notification: notification);
      return;
    }

    //1. Notification is for store
    //2. User view
    if (notification.data["notified_user_or_store"]
                .toString()
                .split("")
                .first ==
            'S' &&
        AppConstants.appData.isUserView!) {
      try {
        // Get store reference from notification
        String storeReference = notification.data['reference'] ?? '';

        // Find the store in the user's store list
        final userStores = BuyerHomeBloc.storeListResponse.storeList ?? [];
        final targetStore = userStores.firstWhere(
          (element) => element.storeReference == storeReference,
          orElse: () =>
              throw Exception('Store not found in user\'s store list'),
        );

        // Show switching message
        CommonMethods.toastMessage(
          "Switching to ${targetStore.storehandle}",
          AppConstants.userStoreCommonBottomNavigationContext,
          toastShowTimer: 3,
        );

        // Switch to seller account
        await CommonMethods.switchToSeller(
          context: AppConstants.userStoreCommonBottomNavigationContext,
          storeReference: storeReference,
          storeId: targetStore.storeid!,
        );

        // Add a small delay to ensure the navigation is complete
        await Future.delayed(const Duration(milliseconds: 500));

        //Navigation - immediate redirection after account switch
        navigateToScreen(notification: notification);
      } catch (e) {
        CommonMethods.toastMessage(
          "Failed to switch to store account",
          AppConstants.userStoreCommonBottomNavigationContext,
        );
      }
      return;
    }

    navigateToScreen(notification: notification);
  }
  //endregion

  ///3
  //region Navigation
  void navigateToScreen({required RemoteMessage notification}) {
    ///NEW_MESSAGE - Navigate to new messaging chat screen
    if (notification.data["notification_type"] == "new_message") {
      // Navigate to new messaging chat screen
      NewMessagingChatScreen.navigateToChat(
          AppConstants.currentSelectedTabContext,
          connectingId: notification.data['sender_id'] ?? '',
          chatName: notification.data['chat_name'] ??
              notification.data['sender_name'] ??
              'User',
          chatIcon: notification.data['chat_icon'] ??
              notification.data['sender_icon'] ??
              '',
          entityType: notification.data['entity_type'] ?? 'USER',
          chatOwnerReference: notification.data['sender_user_reference'] ?? '');
      return;
    }

    //Notification about
    String notificationAbout = notification.data["notification_about"]!;
    //Notification about first letter
    String aboutNotificationFirstLetter = notificationAbout.split("").first;
    //Screen
    late Widget screen;

    //If not mounted
    if (!AppConstants.isBottomNavigationMounted.value) {
      return;
    }

    ///MESSAGE_RECEIVED
    if (notification.data["notification_type"] == "MESSAGE_RECEIVED") {
      //Go to messaging screen
      var screen = MessageDetailScreen(
          toEntityReference: notification.data['notification_about']);
      //notification_about
      var route = MaterialPageRoute(builder: (context) => screen);
      Navigator.push(AppConstants.currentSelectedTabContext, route);
      return;
    }

    //If external_url value is not null
    if (notification.data["external_url"] != null) {
      String url = notification.data["external_url"];
      CommonMethods.openAppWebView(
          webUrl: url,
          context: AppConstants.userStoreCommonBottomNavigationContext);
      return;
    }

    //Navigate to the appropriate screen
    StoreUserProductNavigation()
        .navigateToStoreProductAndStore(references: notificationAbout);

    // //Set the value as per the about notification first letters
    //
    // switch (aboutNotificationFirstLetter) {
    //   case "S":
    //     screen = BuyerViewStoreScreen(storeReference: notificationAbout);
    //     break;
    //   case "U":
    //     screen = UserProfileScreen(userReference: notificationAbout);
    //     break;
    //   case "P":
    //     screen = BuyerViewSingleProductScreen(productReference: notificationAbout);
    //     break;
    //   case "O":
    //     if (!AppConstants.appData.isStoreView!) {
    //       screen = BuyerSubOrderScreen(orderNumber: notificationAbout);
    //     } else {
    //       screen = SellerSubOrderScreen(orderNumber: notificationAbout,storeId: AppConstants.appData.storeId!);
    //     }
    //     break;
    //   default:
    //     break;
    // }
    // //Push to the screen
    // var route = MaterialPageRoute(builder: (context) => screen);
    // Navigator.push(AppConstants.currentSelectedTabContext, route);
  }
//endregion
}
