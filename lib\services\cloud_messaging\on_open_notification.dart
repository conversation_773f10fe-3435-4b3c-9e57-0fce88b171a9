import 'dart:async';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_home/buyer_home_bloc.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/buyer_sub_order/buyer_sub_order_screen.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/buyer_view_single_product/buyer_view_single_product_screen.dart';
import 'package:swadesic/features/buyers/buyer_view_store/buyer_view_store_screen.dart';
import 'package:swadesic/features/buyers/messaging/new_messaging_chat_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/messaging_and_requests/all_messages/message_detail/message_detail_screen.dart';
import 'package:swadesic/features/seller/seller_all_orders/seller_sub_order/seller_sub_order_screen.dart';
import 'package:swadesic/features/user_profile/user_profile_screen.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/store_user_product_navigation/store_user_product_navigation.dart';

class OnOpenAppNotification {
  // late RemoteMessage notification;

  //Constructor
  // OnOpenAppNotification(){
  //
  //   //print("Bottom navigation mounted check");
  //   bottomNavigationMountedCheck(notification: notification);
  //   //Check if the bottom navigation is mounted or not
  //   //If it is mounted then call the navigation method
  //
  // }

  ///1
  //region Bottom navigation mounted check
  void bottomNavigationMountedCheck({required RemoteMessage notification}) {
    print("FCM: Notification tap handler called - isBottomNavigationMounted: ${AppConstants.isBottomNavigationMounted.value}");

    // Check if bottom navigation is already mounted
    if (AppConstants.isBottomNavigationMounted.value) {
      print("FCM: Bottom navigation already mounted, proceeding with navigation");
      checkUserOrStoreView(notification: notification);
      return;
    }

    // For cold start: Wait for bottom navigation to be mounted
    print("FCM: Cold start detected, waiting for bottom navigation to mount");
    _waitForBottomNavigationAndNavigate(notification);
  }

  //region Wait for bottom navigation and navigate (Cold start handling)
  void _waitForBottomNavigationAndNavigate(RemoteMessage notification) {
    print("FCM: Setting up listener for bottom navigation mount");

    // Set up a listener for when bottom navigation becomes available
    late VoidCallback listener;
    bool hasNavigated = false;

    listener = () {
      if (AppConstants.isBottomNavigationMounted.value && !hasNavigated) {
        hasNavigated = true;
        print("FCM: Bottom navigation mounted during cold start, proceeding with navigation");

        // Remove the listener to prevent multiple calls
        try {
          AppConstants.isBottomNavigationMounted.removeListener(listener);
        } catch (e) {
          print("FCM: Error removing listener: $e");
        }

        // Add a small delay to ensure all contexts are properly initialized
        Future.delayed(const Duration(milliseconds: 1000), () {
          if (!hasNavigated) return; // Double check to prevent multiple navigations
          print("FCM: Executing delayed navigation after cold start");
          checkUserOrStoreView(notification: notification);
        });
      }
    };

    // Add the listener
    AppConstants.isBottomNavigationMounted.addListener(listener);

    // Also try polling as a backup mechanism
    _pollForBottomNavigationMount(notification, listener);

    // Fallback timeout in case something goes wrong
    Future.delayed(const Duration(seconds: 15), () {
      if (!hasNavigated) {
        hasNavigated = true;
        try {
          AppConstants.isBottomNavigationMounted.removeListener(listener);
          print("FCM: Timeout reached, attempting direct navigation");
          // Try direct navigation as last resort
          checkUserOrStoreView(notification: notification);
        } catch (e) {
          print("FCM: Error in timeout fallback: $e");
        }
      }
    });
  }

  //region Poll for bottom navigation mount (backup mechanism)
  void _pollForBottomNavigationMount(RemoteMessage notification, VoidCallback listener) {
    int attempts = 0;
    const maxAttempts = 30; // 30 seconds with 1 second intervals

    Timer.periodic(const Duration(seconds: 1), (timer) {
      attempts++;
      print("FCM: Polling attempt $attempts - isBottomNavigationMounted: ${AppConstants.isBottomNavigationMounted.value}");

      if (AppConstants.isBottomNavigationMounted.value) {
        timer.cancel();
        print("FCM: Bottom navigation detected via polling");
        // Trigger the listener manually
        listener();
      } else if (attempts >= maxAttempts) {
        timer.cancel();
        print("FCM: Polling timeout reached");
      }
    });
  }
  //endregion
  //endregion
  //endregion

  ///2
  //region Check user or store view
  //If notification is for user and view is in store then switch to user
  void checkUserOrStoreView({required RemoteMessage notification}) async {
    print("FCM: checkUserOrStoreView called");
    print("FCM: Notification data: ${notification.data}");
    print("FCM: App data - isUserView: ${AppConstants.appData.isUserView}, isStoreView: ${AppConstants.appData.isStoreView}");

    // For debugging, let's skip account switching for now and go directly to navigation
    // navigateToScreen(notification: notification);
    // return;
    //1. Notification is for user
    //2. Store view
    if (notification.data["notified_user_or_store"]
                .toString()
                .split("")
                .first ==
            'U' &&
        AppConstants.appData.isStoreView!) {
      // Show switching message
      CommonMethods.toastMessage(
        "Switching to user account",
        AppConstants.userStoreCommonBottomNavigationContext,
        toastShowTimer: 3,
      );

      // Switch to buyer account
      await CommonMethods.switchToBuyer(
          context: AppConstants.userStoreCommonBottomNavigationContext);

      // Add a small delay to ensure the navigation is complete
      await Future.delayed(const Duration(milliseconds: 500));

      //Navigation - immediate redirection after account switch
      navigateToScreen(notification: notification);
      return;
    }

    //1. Notification is for store
    //2. User view
    if (notification.data["notified_user_or_store"]
                .toString()
                .split("")
                .first ==
            'S' &&
        AppConstants.appData.isUserView!) {
      try {
        // Get store reference from notification
        String storeReference = notification.data['reference'] ?? '';

        // Find the store in the user's store list
        final userStores = BuyerHomeBloc.storeListResponse.storeList ?? [];
        final targetStore = userStores.firstWhere(
          (element) => element.storeReference == storeReference,
          orElse: () =>
              throw Exception('Store not found in user\'s store list'),
        );

        // Show switching message
        CommonMethods.toastMessage(
          "Switching to ${targetStore.storehandle}",
          AppConstants.userStoreCommonBottomNavigationContext,
          toastShowTimer: 3,
        );

        // Switch to seller account
        await CommonMethods.switchToSeller(
          context: AppConstants.userStoreCommonBottomNavigationContext,
          storeReference: storeReference,
          storeId: targetStore.storeid!,
        );

        // Add a small delay to ensure the navigation is complete
        await Future.delayed(const Duration(milliseconds: 500));

        //Navigation - immediate redirection after account switch
        navigateToScreen(notification: notification);
      } catch (e) {
        CommonMethods.toastMessage(
          "Failed to switch to store account",
          AppConstants.userStoreCommonBottomNavigationContext,
        );
      }
      return;
    }

    print("FCM: Calling navigateToScreen");
    navigateToScreen(notification: notification);
  }
  //endregion

  ///3
  //region Navigation
  void navigateToScreen({required RemoteMessage notification}) {
    print("FCM: Starting navigation for notification type: ${notification.data["notification_type"]}");

    // Validate that we have the required notification data
    if (notification.data["notification_about"] == null &&
        notification.data["notification_type"] != "new_message" &&
        notification.data["external_url"] == null) {
      print("FCM: Missing required notification data, aborting navigation");
      return;
    }

    ///NEW_MESSAGE - Navigate to new messaging chat screen
    if (notification.data["notification_type"] == "new_message") {
      _handleNewMessageNavigation(notification);
      return;
    }

    ///MESSAGE_RECEIVED
    if (notification.data["notification_type"] == "MESSAGE_RECEIVED") {
      _handleMessageReceivedNavigation(notification);
      return;
    }

    //If external_url value is not null
    if (notification.data["external_url"] != null) {
      _handleExternalUrlNavigation(notification);
      return;
    }

    // Handle standard navigation (posts, products, stores, users, orders)
    _handleStandardNavigation(notification);
  }

  //region Handle new message navigation
  void _handleNewMessageNavigation(RemoteMessage notification) {
    try {
      print("FCM: Handling new message navigation");

      // Validate context availability
      if (!_isContextAvailable()) {
        print("FCM: Context not available for new message navigation");
        return;
      }

      NewMessagingChatScreen.navigateToChat(
          AppConstants.currentSelectedTabContext,
          connectingId: notification.data['sender_id'] ?? '',
          chatName: notification.data['chat_name'] ??
              notification.data['sender_name'] ??
              'User',
          chatIcon: notification.data['chat_icon'] ??
              notification.data['sender_icon'] ??
              '',
          entityType: notification.data['entity_type'] ?? 'USER',
          chatOwnerReference: notification.data['sender_user_reference'] ?? '');
    } catch (e) {
      print("FCM: Error in new message navigation: $e");
    }
  }
  //endregion

  //region Handle message received navigation
  void _handleMessageReceivedNavigation(RemoteMessage notification) {
    try {
      print("FCM: Handling message received navigation");

      if (!_isContextAvailable()) {
        print("FCM: Context not available for message received navigation");
        return;
      }

      var screen = MessageDetailScreen(
          toEntityReference: notification.data['notification_about']);
      var route = MaterialPageRoute(builder: (context) => screen);
      Navigator.push(AppConstants.currentSelectedTabContext, route);
    } catch (e) {
      print("FCM: Error in message received navigation: $e");
    }
  }
  //endregion

  //region Handle external URL navigation
  void _handleExternalUrlNavigation(RemoteMessage notification) {
    try {
      print("FCM: Handling external URL navigation");

      String url = notification.data["external_url"];

      // Use global navigator context as fallback for external URLs
      BuildContext? context = AppConstants.userStoreCommonBottomNavigationContext;

      CommonMethods.openAppWebView(webUrl: url, context: context);
    } catch (e) {
      print("FCM: Error in external URL navigation: $e");
    }
  }
  //endregion

  //region Handle standard navigation (posts, products, stores, users, orders)
  void _handleStandardNavigation(RemoteMessage notification) {
    try {
      print("FCM: Handling standard navigation");

      String notificationAbout = notification.data["notification_about"]!;
      print("FCM: Navigation target: $notificationAbout");

      // Validate that bottom navigation is mounted
      if (!AppConstants.isBottomNavigationMounted.value) {
        print("FCM: Bottom navigation not mounted, cannot proceed with standard navigation");
        return;
      }

      // Use the store/user/product navigation utility
      StoreUserProductNavigation()
          .navigateToStoreProductAndStore(references: notificationAbout);
    } catch (e) {
      print("FCM: Error in standard navigation: $e");
    }
  }
  //endregion

  //region Check if context is available
  bool _isContextAvailable() {
    try {
      // Check if bottom navigation is mounted
      if (!AppConstants.isBottomNavigationMounted.value) {
        print("FCM: Bottom navigation not mounted");
        return false;
      }

      // For navigation, we can use multiple fallback contexts
      // So we don't need to be too strict here
      print("FCM: Context availability check passed");
      return true;
    } catch (e) {
      print("FCM: Error checking context availability: $e");
      return false;
    }
  }
  //endregion
  //endregion
}
