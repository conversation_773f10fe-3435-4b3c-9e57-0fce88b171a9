import 'package:flutter/material.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_widgets.dart';

class MiniDashboardTooltipInfo extends StatefulWidget {
  const MiniDashboardTooltipInfo({super.key});

  @override
  State<MiniDashboardTooltipInfo> createState() =>
      _MiniDashboardTooltipInfoState();
}

class _MiniDashboardTooltipInfoState extends State<MiniDashboardTooltipInfo> {
  List<Map<String, String>> imageAndTitle = [
    {
      'image': AppImages.waitingForConfirmation,
      'title': 'Waiting for confirmation',
    },
    {
      'image': AppImages.confirmedNotYetShipped,
      'title': 'Confirmed, not yet shipped',
    },
    {
      'image': AppImages.delivered,
      'title': 'Delivered',
    },
    {
      'image': AppImages.shippingInProgress,
      'title': 'Shipping in progress',
    },
    {
      'image': AppImages.cancelled,
      'title': 'Cancelled',
    },
    {
      'image': AppImages.returnRequested,
      'title': 'Return requested',
    },
    {
      'image': AppImages.returnPickup,
      'title': 'Return to pick-up',
    },
    {
      'image': AppImages.returned,
      'title': 'Returned',
    },
    {
      'image': AppImages.refunded,
      'title': 'Refunded',
    },
    {
      'image': AppImages.refundHold,
      'title': 'Refund hold',
    },
  ];

  @override
  Widget build(BuildContext context) {
    return body();
  }

  //region Body
  Widget body() {
    final screenWidth = MediaQuery.of(context).size.width;
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 16),
      constraints: BoxConstraints(maxWidth: screenWidth * 0.9),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Suborders are product level orders inside an order from Customer.',
            style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
          ),
          verticalSizedBox(30),
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: imageAndTitle.length,
            itemBuilder: (context, index) {
              return Container(
                margin: const EdgeInsets.only(bottom: 10),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Image.asset(
                      imageAndTitle[index]['image']!,
                      height: 30,
                    ),
                    const SizedBox(
                      width: 10,
                    ),
                    Text(
                      imageAndTitle[index]['title']!,
                      style: AppTextStyle.contentText0(
                          textColor: AppColors.appBlack),
                    )
                  ],
                ),
              );
            },
          ),
        ],
      ),
    );
  }
//endregion
}
