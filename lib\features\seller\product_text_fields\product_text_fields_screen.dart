import 'package:flutter/material.dart';
import 'package:swadesic/features/seller/add_edit_product_fields/add_edit_product_fields.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_widgets.dart';

class ProductTextFieldsScreen extends StatefulWidget {
  final bool isEditMode;
  final ProductDetailsSection section;

  const ProductTextFieldsScreen({
    Key? key,
    this.isEditMode = false,
    this.section = ProductDetailsSection.basic,
  }) : super(key: key);

  @override
  State<ProductTextFieldsScreen> createState() =>
      _ProductTextFieldsScreenState();
}

class _ProductTextFieldsScreenState extends State<ProductTextFieldsScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.appWhite,
      appBar: appBar(),
      body: SafeArea(
        child: LayoutBuilder(
          builder: (context, constraints) => SingleChildScrollView(
            padding: const EdgeInsets.only(left: 15, right: 15, bottom: 20),
            child: ConstrainedBox(
              constraints: BoxConstraints(
                minHeight:
                    constraints.maxHeight - 20, // Account for bottom padding
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // verticalSizedBox(20),
                  AddEditProductFields(section: widget.section),
                  // Add some bottom padding when keyboard is visible
                  SizedBox(
                      height: MediaQuery.of(context).viewInsets.bottom > 0
                          ? 300
                          : 0),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  AppBar appBar() {
    return AppCommonWidgets.mainAppBar(
      context: context,
      isCustomTitle: false,
      title: _titleForSection(),
      isDefaultMenuVisible: false,
      isMembershipVisible: false,
      isCartVisible: false,
    );
  }

  String _titleForSection() {
    switch (widget.section) {
      case ProductDetailsSection.basic:
        return 'Basic Details';
      case ProductDetailsSection.promotions:
        return 'Promotions & Offers';
      case ProductDetailsSection.visibility:
        return 'Discovery & Tags';
      case ProductDetailsSection.moreDetails:
        return 'Additional Info';
      case ProductDetailsSection.all:
      default:
        return widget.isEditMode
            ? AppStrings.editProduct
            : AppStrings.addProduct;
    }
  }
}
