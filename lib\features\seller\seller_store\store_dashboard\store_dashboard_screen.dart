import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutter_switch/flutter_switch.dart';
import 'package:percent_indicator/circular_percent_indicator.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/data_model/store_dashboard_data_model/store_config_data_model.dart';
import 'package:swadesic/features/data_model/store_dashboard_data_model/store_dashboard_data_model.dart';
import 'package:swadesic/features/seller/seller_store/store_dashboard/complete_check_list/complete_check_list.dart';
import 'package:swadesic/features/seller/seller_store/store_dashboard/mini_dashboard_info/mini_dashboard_info.dart';
import 'package:swadesic/features/seller/seller_store/store_dashboard/modular_store_dashboard_container.dart';
import 'package:swadesic/features/seller/seller_store/store_dashboard/store_activate_and_open_card/store_activate_and_open_card.dart';
import 'package:swadesic/features/seller/seller_store/store_dashboard/store_valuation_widget.dart';
import 'package:swadesic/features/seller/unified_product_management/unified_product_management_screen.dart';
import 'package:swadesic/model/affiliate_program_response/affiliate_program_detail.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'store_dashboard_bloc.dart';

// region Store DashBoard
class StoreDashBoardScreen extends StatefulWidget {
  final String storeReference;

  const StoreDashBoardScreen({
    Key? key,
    required this.storeReference,
  }) : super(key: key);

  @override
  _StoreDashBoardScreenState createState() => _StoreDashBoardScreenState();
}
// endregion

class _StoreDashBoardScreenState extends State<StoreDashBoardScreen>
    with AutomaticKeepAliveClientMixin<StoreDashBoardScreen> {
  //Keep alive
  @override
  bool get wantKeepAlive => true;

  // region Bloc
  late StoreDashBoardBloc storeDashBoardBloc;

  // endregion

  // region Init
  @override
  void initState() {
    storeDashBoardBloc = StoreDashBoardBloc(context, widget.storeReference);
    storeDashBoardBloc.init();
    super.initState();
  }

  // endregion

  //region Dispose
  @override
  void dispose() {
    storeDashBoardBloc.dispose();
    super.dispose();
  }

  //endregion

  // region build
  @override
  Widget build(BuildContext context) {
    return body();
    // return RefreshIndicator(
    //   color: AppColors.brandGreen,
    //   onRefresh: () async {
    //     await storeDashBoardBloc.getStoreDashboard();
    //   },
    //   child: StreamBuilder<StoreDashBoardState>(
    //       stream: storeDashBoardBloc.storeDashboardCtrl.stream,
    //       initialData: StoreDashBoardState.Loading,
    //       builder: (context, snapshot) {
    //         //print(snapshot.data);
    //         if (snapshot.data == StoreDashBoardState.Failed) {
    //           return Scaffold(
    //               backgroundColor: AppColors.appWhite,
    //               body: SizedBox(
    //                 height: MediaQuery
    //                     .of(context)
    //                     .size
    //                     .height / 2,
    //                 child: AppCommonWidgets.errorWidget(
    //                     errorMessage: AppStrings.unableToLoadProducts,
    //                     onTap: () {
    //                       storeDashBoardBloc.getStoreDashboard();
    //                     }),
    //               )
    //           );
    //         }
    //         if (snapshot.data == StoreDashBoardState.Loading) {
    //           return Scaffold(
    //               backgroundColor: AppColors.appWhite,
    //               body: Center(
    //                   child: AppCommonWidgets.appCircularProgress()
    //               ));
    //         }
    //         if (snapshot.data == StoreDashBoardState.Success) {
    //           return Scaffold(
    //             backgroundColor: AppColors.appWhite,
    //
    //             ///appBar: appBar(),
    //             // body: SafeArea(child:body()),
    //             body: SafeArea(
    //                 child: body()),
    //           );
    //         }
    //         return const SizedBox();
    //       }),
    // );
  }

  // endregion

  // region Body
  Widget body() {
    return Padding(
      padding: const EdgeInsets.only(top: 2),
      child: RefreshIndicator(
        color: AppColors.brandBlack,
        onRefresh: () async {
          storeDashBoardBloc.getStoreDashboard();
        },
        child: ListView(
          padding: const EdgeInsets.all(0.0),
          shrinkWrap: true,
          children: [
            // const StoreValuationWidget(),
            // StoreActivateAndOpenCard(
            //   storeReference: AppConstants.appData.storeReference!,
            //   isFromMiniDashBoardScreen: true,
            // ),
            // //activeInActiveStore(),
            // const MiniDashboard(),
            ModularStoreDashboardContainer(
              storeReference: AppConstants.appData.storeReference!,
            ),
            // verticalSizedBox(16),
            // messAnnounce(),
            // verticalSizedBox(16),
            access(),
            AppCommonWidgets.bottomListSpace(context: context)
          ],
        ),
      ),
    );
  }

  // endregion

//region Store activation check list
//   Widget storeActivationCheckList() {
//     return Consumer<StoreDashboardDataModel>(
//       builder: (BuildContext context, data, Widget? child) {
//         return Column(
//           children: [
//             ///Open and close
//             Visibility(
//               visible: data.storeDashBoard.isActive!,
//               child: Container(
//                 margin: const EdgeInsets.only(top: 10, left: 6, right: 6),
//                 padding: const EdgeInsets.symmetric(
//                     horizontal: 10,
//                     vertical: 20
//                 ),
//                 decoration: AppCommonWidgets.shadowDecoration(),
//                 child: Row(
//                   children: [
//                     Expanded(
//                       child: Text(
//                           !data.storeDashBoard.isActive!
//                               ? "Your store is not yet activated"
//                               : data.storeDashBoard.openForOrder!
//                               ? AppStrings.yourStoreOpen
//                               : AppStrings.yourStoreClose,
//                           style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack)),
//                     ),
//                     Padding(
//                       padding: const EdgeInsets.only(right: 16),
//                       child: StreamBuilder<bool>(
//                           stream: storeDashBoardBloc.storeOnlineCtrl.stream,
//                           builder: (context, snapshot) {
//                             return Consumer<StoreConfigDataModel>(
//                               builder: (BuildContext context, StoreConfigDataModel storeConfigDataModel, Widget? child) {
//                                 return SizedBox(
//                                   height: 21,
//                                   width: 43.24,
//                                   child: FittedBox(
//                                     child: FlutterSwitch(
//                                       width: 43.24,
//                                       height: 21.0,
//                                       toggleSize: 21,
//                                       borderRadius: 21.0,
//                                       padding: 0.0,
//                                       activeColor: AppColors.lightGray,
//                                       inactiveColor: AppColors.lightGray,
//                                       toggleColor: data.storeDashBoard.dashboardProgress == 100 &&
//                                           data.storeDashBoard.isActive!
//                                           ? AppColors.inActiveGreen
//                                           : AppColors.darkStroke,
//                                       activeToggleColor: AppColors.brandGreen,
//                                       value: data.storeDashBoard.openForOrder!,
//                                       onToggle: (value) {
//                                         //Check is store config allow to open for order
//                                         if(storeConfigDataModel.storeConfig.enableOrders!){
//                                           storeDashBoardBloc.storeOnlineOffline(!value);
//                                         }
//                                         else{
//                                           CommonMethods.toastMessage(AppStrings.swadesicOrderIsSoonComing, context,toastShowTimer: 5);
//                                         }
//                                       }
//                                           , // Provide a no-op function when the condition is false
//                                     ),
//                                   ),
//                                 );
//                               },
//                             );
//
//                           }
//                       ),
//                     )
//                   ],
//                 ),
//               ),
//             ),
//
//             ///Complete/Go live
//             Visibility(
//               visible: !data.storeDashBoard.isActive!,
//               child: Container(
//                 margin: const EdgeInsets.only(top: 10, left: 6, right: 6),
//                 padding: const EdgeInsets.all(10),
//                 decoration: AppCommonWidgets.shadowDecoration(),
//                 child: Row(
//                   mainAxisSize: MainAxisSize.min,
//                   mainAxisAlignment: MainAxisAlignment.center,
//                   crossAxisAlignment: CrossAxisAlignment.center,
//                   children: [
//                     //Store activation
//                     Text(
//                       AppStrings.storeActivation,
//                       style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
//                     ),
//                     //Progress
//                     Padding(
//                       padding: const EdgeInsets.symmetric(horizontal: 15),
//                       child: CircularPercentIndicator(
//                         radius: 20.0,
//                         lineWidth: 5.0,
//                         percent: data.storeDashBoard.dashboardProgress / 100,
//                         backgroundColor: AppColors.brandGreen.withOpacity(0.1),
//                         center: appText(
//                           "${data.storeDashBoard.dashboardProgress.round()}%",
//                           color: AppColors.appBlack,
//                           maxLine: 1,
//                           fontFamily: AppConstants.rRegular,
//                           fontSize: 10,
//                           fontWeight: FontWeight.w500,
//                         ),
//                         progressColor: AppColors.brandGreen,
//                       ),
//                     ),
//                     const Expanded(child: SizedBox()),
//                     //Button
//                     CupertinoButton(
//                       borderRadius: BorderRadius.circular(100),
//                       color: AppColors.brandGreen,
//                       padding: EdgeInsets.zero,
//                       child: Container(
//                           padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 20),
//                           child: Text(data.storeDashBoard.dashboardProgress > 99 ? AppStrings.goLiveNow : AppStrings.complete,
//                             style: AppTextStyle.access0(textColor: AppColors.appWhite),)),
//                       onPressed: () {
//                         //If progress is 99 then call active api
//                         data.storeDashBoard.dashboardProgress > 99 ?
//                         storeDashBoardBloc.activeDeActiveApiCall()
//                         //Else open bottom sheet
//                             : CommonMethods.appMinimumBottomSheets(
//                             bottomSheetName: AppStrings.completeStoreActivation,
//                             screen:
//                             CompleteCheckList(
//                               storeDashBoard: data.storeDashBoard,
//                               previousScreenContext: context,
//                               storeId: AppConstants.appData.storeId!,
//                             ), context: context);
//                       },
//                     )
//                   ],
//                 ),
//               ),
//             ),
//
//           ],
//         );
//       },
//     );
//   }

  //endregion

//region New Order and Return
//   Widget newReturn() {
//     return Consumer<StoreDashboardDataModel>(builder: (BuildContext context,data, Widget? child) {
//
//       return Visibility(
//         visible: data.storeDashBoard.isActive!,
//         child: Padding(
//           padding: const EdgeInsets.symmetric(
//             horizontal: 6,
//           ),
//           child: Row(
//             mainAxisAlignment: MainAxisAlignment.spaceBetween,
//             children: [
//               Expanded(
//                 child: CupertinoButton(
//                   padding: const EdgeInsets.all(0.0),
//                   onPressed: () {
//                     storeDashBoardBloc.goToSellerAllOrder();
//                   },
//                   child: Container(
//                     padding: const EdgeInsets.symmetric(
//                       horizontal: 10,
//                       vertical: 10,
//                     ),
//                     decoration: AppCommonWidgets.shadowDecoration(),
//                     child: Column(
//                       mainAxisAlignment: MainAxisAlignment.start,
//                       mainAxisSize: MainAxisSize.min,
//                       crossAxisAlignment: CrossAxisAlignment.start,
//                       children: [
//                         //region NewOrder icon and text
//                         Row(
//                           children: [
//                             SizedBox(
//                                 height: 32, width: 32,
//                                 child: Image.asset(AppImages.waitingOrdersPng, height: 32, width: 32, fit: BoxFit.fill,)),
//                             horizontalSizedBox(5),
//                             Expanded(
//                               child: Row(
//                                 children: [
//                                   Expanded(
//                                     child: Text(AppStrings.waitingOrders,
//                                         maxLines: 1,
//                                         overflow: TextOverflow.ellipsis,
//                                         style: AppTextStyle.access0(textColor: AppColors.appBlack)),
//                                   ),
//                                 ],
//                               ),
//                             )
//                           ],
//                         ),
//                         //endregion
//
//                         verticalSizedBox(10),
//
//                         //region Green up Arrow and Number of order
//                         Row(
//                           mainAxisAlignment: MainAxisAlignment.start,
//                           crossAxisAlignment: CrossAxisAlignment.center,
//                           children: [
//                             SvgPicture.asset(
//                               AppImages.newOrderUpArrow,
//                               color: AppColors.discountGreen,
//                             ),
//                             horizontalSizedBox(10),
//                             Text("${data.storeDashBoard.newOrders}",
//                                 style: AppTextStyle.pageHeading(textColor: AppColors.brandGreen))
//                           ],
//                         ),
//                         //endregion
//                       ],
//                     ),
//                   ),
//                 ),
//               ),
//               horizontalSizedBox(15),
//
//               ///Return and cancel
//               Expanded(
//                 child: CupertinoButton(
//                   padding: const EdgeInsets.all(0.0),
//                   onPressed: () {
//                     storeDashBoardBloc.goToSellerAllOrder();
//                   },
//                   child: Container(
//                     padding: const EdgeInsets.symmetric(
//                       horizontal: 10,
//                       vertical: 10,
//                     ),
//                     decoration: AppCommonWidgets.shadowDecoration(),
//                     child: Column(
//                       mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                       mainAxisSize: MainAxisSize.min,
//                       children: [
//                         //region Returns icon and text
//                         Row(
//                           children: [
//                             SvgPicture.asset(
//                               AppImages.returnCancelOrder,
//                               height: 32,
//                               width: 32,
//                             ),
//                             horizontalSizedBox(5),
//                             Expanded(
//                               child: Row(
//                                 children: [
//                                   Expanded(child: Text(AppStrings.returnsAndCancel,
//                                       maxLines: 1,
//                                       overflow: TextOverflow.ellipsis,
//                                       style: AppTextStyle.access0(textColor: AppColors.appBlack))),
//                                 ],
//                               ),
//                             )
//                           ],
//                         ),
//                         //endregion
//
//                         verticalSizedBox(10),
//
//                         //region Red Down Arrow
//                         Row(
//                           mainAxisAlignment: MainAxisAlignment.start,
//                           crossAxisAlignment: CrossAxisAlignment.center,
//                           children: [
//                             SvgPicture.asset(
//                               AppImages.returnDown,
//                               color: AppColors.red,
//                               height: 8.42,
//                               width: 13.04,
//                             ),
//                             horizontalSizedBox(12.48),
//                             Text(
//                               "${data.storeDashBoard.returnCompleted}",
//                               style: AppTextStyle.pageHeading(textColor: AppColors.red),
//                             ),
//                             horizontalSizedBox(23.48),
//                             SvgPicture.asset(
//                               AppImages.returnDown,
//                               color: AppColors.orange,
//                               height: 8.42,
//                               width: 13.04,
//                             ),
//                             horizontalSizedBox(12.48),
//                             Text(
//                               "${data.storeDashBoard.returnInProgress}",
//                               style: AppTextStyle.pageHeading(textColor: AppColors.orange),
//                             ),
//                           ],
//                         ), //endregion
//                       ],
//                     ),
//                   ),
//                 ),
//               ),
//             ],
//           ),
//         ),
//       );
//
//     },
//     );
//   }

//endregion

  //region Complete the

//region Access
  Widget access() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        ///Find your customers on swadesic
        // AppCommonWidgets.settingOption(
        //     horizontalPadding: 10,
        //     prefixIconPadding: 5,
        //     optionText: AppStrings.findYourCustomersOnSwadesic,
        //     prefixIcon: AppImages.findYourFriendsIcon,
        //     onTap: () {
        //       storeDashBoardBloc.goToFindYourCustomer();
        //     }),

        // ///Add or Edit product
        // AppCommonWidgets.settingOption(
        //     horizontalPadding: 10,
        //     prefixIconPadding: 5,
        //     optionText: "Add or Edit Product",
        //     prefixIcon: AppImages.addProduct,
        //     onTap: () {
        //       _navigateToUnifiedProductManagement();
        //     }),

        ///Add product
        AppCommonWidgets.settingOption(
            horizontalPadding: 10,
            prefixIconPadding: 5,
            optionText: AppStrings.addProduct,
            prefixIcon: AppImages.addProduct,
            onTap: () {
              storeDashBoardBloc.goToAddProduct();
            }),

        ///Edit product
        AppCommonWidgets.settingOption(
            horizontalPadding: 10,
            prefixIconPadding: 5,
            optionText: AppStrings.editProduct,
            prefixIcon: AppImages.editProduct,
            onTap: () {
              storeDashBoardBloc.goToEditProduct();
            }),
        // ///Affiliate program
        // AppCommonWidgets.settingOption(
        //     subOption: Text(AffiliateProgramDetail().title!,
        //       maxLines: 1,
        //       overflow: TextOverflow.ellipsis,
        //       style: AppTextStyle.smallText(textColor: AppColors.brandGreen),),
        //
        //     horizontalPadding: 10,
        //     prefixIconPadding: 5,
        //     optionText: AppStrings.affiliateProgram,
        //     prefixIcon: AppImages.affiliateIcon,
        //     onTap: () {
        //       storeDashBoardBloc.goToAffiliateProgram();
        //     }),

        // ///Supported stores
        // AppCommonWidgets.settingOption(
        //     horizontalPadding: 10,
        //
        //     prefixIconPadding: 5,
        //     optionText: AppStrings.supportedStores,
        //     prefixIcon: AppImages.supportStore,
        //     onTap: () {
        //       storeDashBoardBloc.onTapFavouriteStore();
        //     }),
        // ///Recently visited stores
        // AppCommonWidgets.settingOption(
        //     horizontalPadding: 10,
        //
        //     prefixIconPadding: 5,
        //     optionText: AppStrings.recentStore,
        //     prefixIcon: AppImages.recentlyVisitedStore,
        //     onTap: () {
        //       storeDashBoardBloc.onTapRecentlyVisitedStore();
        //     }),
        ///All orders  order
        AppCommonWidgets.settingOption(
            horizontalPadding: 10,
            prefixIconPadding: 5,
            optionText: AppStrings.allOrders,
            prefixIcon: AppImages.orders,
            onTap: () {
              storeDashBoardBloc.goToSellerAllOrder();
            }),

        ///Account balance and rewards
        AppCommonWidgets.settingOption(
            horizontalPadding: 10,
            prefixIconPadding: 5,
            optionText: AppStrings.accountBalanceAndRewards,
            prefixIcon: AppImages.accountBalance,
            onTap: () {
              storeDashBoardBloc.goToAccountBalance();
            }),

        ///Support
        AppCommonWidgets.settingOption(
            horizontalPadding: 10,
            prefixIconPadding: 5,
            optionText: AppStrings.support,
            prefixIcon: AppImages.support,
            onTap: () {
              storeDashBoardBloc.onTapGoToSupport(
                isReport: true,
                targetStoreReference: widget.storeReference,
              );
            }),

        ///Suggest a feature
        AppCommonWidgets.settingOption(
            horizontalPadding: 10,
            prefixIconPadding: 5,
            optionText: AppStrings.suggestAnIdea,
            prefixIcon: AppImages.supportIdea,
            onTap: () {
              storeDashBoardBloc.onTapGoToSupport(
                isReport: false,
                // targetStoreReference: widget.storeReference,
              );
            }),

        ///Create store
        // AppCommonWidgets.settingOption(
        //     horizontalPadding: 10,
        //     prefixIconPadding: 5,
        //     optionText: AppStrings.createStore,
        //     prefixIcon: AppImages.createStore,
        //     onTap: () {
        //       storeDashBoardBloc.goToSellerAccountScreen();
        //     }),
        ///Switch account
        AppCommonWidgets.settingOption(
            horizontalPadding: 10,
            prefixIconPadding: 5,
            optionText: AppStrings.switchAccount,
            prefixIcon: AppImages.switchAccount,
            onTap: () {
              storeDashBoardBloc.goToSellerAccountScreen();
            }),

        ///Invitees
        AppCommonWidgets.settingOption(
            horizontalPadding: 10,
            prefixIconPadding: 5,
            optionText: AppStrings.myInvitees,
            prefixIcon: AppImages.invitees,
            onTap: () {
              storeDashBoardBloc.goToInvitees();
            }),

        ///Swadesic Blog
        AppCommonWidgets.settingOption(
            horizontalPadding: 10,
            prefixIconPadding: 5,
            optionText: AppStrings.swadesicBlog,
            prefixIcon: AppImages.swadesicBlog,
            onTap: () {
              storeDashBoardBloc.goToSwadesicBlog();
            }),

        ///Rewards
        // AppCommonWidgets.settingOption(
        //     horizontalPadding: 10,
        //
        //     prefixIconPadding: 5,
        //     optionText: AppStrings.reward,
        //     prefixIcon: AppImages.switchAccount,
        //     onTap: () {
        //       storeDashBoardBloc.goToSellerRewards();
        //     }),
      ],
    );
  }

  // Navigate to unified product management
  void _navigateToUnifiedProductManagement() {
    // Get the store ID from AppConstants since it's not in the dashboard response
    if (AppConstants.appData.storeId == null) {
      CommonMethods.toastMessage(
          'Unable to get store information. Please try again.', context);
      return;
    }

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => UnifiedProductManagementScreen(
          storeId: AppConstants.appData.storeId!,
          storeReference: widget.storeReference,
          productReference: null, // null for add mode
        ),
      ),
    );
  }

//endregion
}
