import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/buyer_product_common_widgets.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/buyer_view_product_list/buy_button/buy_button.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/buyer_view_single_product/buyer_view_single_product_bloc.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/product_availability/product_availability.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/product_label_dropdown/product_label_dropdown.dart';
import 'package:swadesic/features/buyers/buyer_view_store/buyer_view_store_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/product_detail_full_card/product_detail_full_card.dart';
import 'package:swadesic/features/data_model/shopping_cart_quantity_data_model/shopping_cart_quantity_data_model.dart';
import 'package:swadesic/features/widgets/custom_image_container/custom_image_container.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/app_tool_tip/app_tool_tip.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

// region Buyer View Single Product Screen
class BuyerViewSingleProductScreen extends StatefulWidget {
  final String? productReference;
  final String? productVersion;
  final String? productLatestVersion;
  final String? subOrderNumber;
  final bool? isGoToLatestVisible;

  //final StoreProductResponse storeProductResponse;

  const BuyerViewSingleProductScreen(
      {Key? key,
      this.productReference,
      this.productVersion,
      this.productLatestVersion,
      this.subOrderNumber,
      this.isGoToLatestVisible = false})
      : super(key: key);

  @override
  _BuyerViewSingleProductScreenState createState() =>
      _BuyerViewSingleProductScreenState();
}
// endregion

class _BuyerViewSingleProductScreenState
    extends State<BuyerViewSingleProductScreen> {
  // region Bloc
  late BuyerViewSingleProductBloc buyerViewSingleProductBloc;

  // endregion

  // region Init
  @override
  void initState() {
    print("BuyerViewSingleProductScreen: Initializing with productReference: ${widget.productReference}");

    // Validate required parameters
    if (widget.productReference == null || widget.productReference!.isEmpty) {
      print("BuyerViewSingleProductScreen: Error - productReference is null or empty");
      // Handle error case - could show error screen or navigate back
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted && Navigator.canPop(context)) {
          Navigator.pop(context);
        }
      });
      super.initState();
      return;
    }

    try {
      buyerViewSingleProductBloc = BuyerViewSingleProductBloc(
          context,
          widget.productReference!,
          widget.productVersion ?? "",
          widget.productLatestVersion ?? "",
          widget.subOrderNumber ?? "");
      buyerViewSingleProductBloc.init();
    } catch (e) {
      print("BuyerViewSingleProductScreen: Error initializing bloc: $e");
    }

    super.initState();
  }

  // endregion

  //region Dispose
  @override
  // void dispose() {
  //   for(var data in widget.productList){
  //     for(var url in data.prodImages!){
  //       //print(url.productImage);
  //       CachedNetworkImage.evictFromCache(url.productImage!);
  //       //print("cleared");
  //     }
  //
  //   }
  //
  //   imageCache.clear();
  //   //buyerViewSingleProductBloc.dispose();
  //   // TODO: implement dispose
  //   super.dispose();
  // }
  //endregion

  // region build
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.appWhite,
      appBar: appBar(),
      resizeToAvoidBottomInset: false,
      body: StreamBuilder<BuyerViewSingleProductState>(
          stream: buyerViewSingleProductBloc.singleProductViewCtrl.stream,
          initialData: BuyerViewSingleProductState.Loading,
          builder: (context, snapshot) {
            if (snapshot.data == BuyerViewSingleProductState.Success) {
              return body();
            }
            if (snapshot.data == BuyerViewSingleProductState.Loading) {
              return AppCommonWidgets.appCircularProgress();
            }
            // if (snapshot.data == BuyerViewSingleProductState.Deleted) {
            //   return Container(
            //       alignment: Alignment.center,
            //       height: MediaQuery.of(context).size.height,
            //       child: Text(
            //         AppStrings.thisProductIsNoLongerAvailable,
            //         style: AppTextStyle.smallText(textColor: AppColors.appBlack),
            //       ));
            // }
            return SizedBox(
              height: MediaQuery.of(context).size.height,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  AppCommonWidgets.errorWidget(
                      height: MediaQuery.of(context).size.height,
                      onTap: () {
                        buyerViewSingleProductBloc.init();
                      }),
                ],
              ),
            );
          }),
    );
  }

  // endregion

  //region Appbar
  AppBar appBar() {
    return AppCommonWidgets.mainAppBar(
      context: context,
      isCustomTitle: true,
      customTitleWidget: StreamBuilder<BuyerViewSingleProductState>(
        stream: buyerViewSingleProductBloc.singleProductViewCtrl.stream,
        builder: (context, snapshot) {
          if (snapshot.data == BuyerViewSingleProductState.Loading) {
            return const SizedBox();
          }
          if (snapshot.data == BuyerViewSingleProductState.Success) {
            return Row(
              children: [
                // ClipRRect(
                //   borderRadius: BorderRadius.circular(15),
                //   child: SizedBox(
                //       height: 30,
                //       width: 30,
                //       child: extendedImage(buyerViewSingleProductBloc.getSingleProductDetailResponse.singleProduct!.storeIcon,context,50,50,cache: true)),
                // ),
                // horizontalSizedBox(10),
                AppCommonWidgets.appBarTitleText(
                  text: buyerViewSingleProductBloc
                      .getSingleProductDetailResponse
                      .singleProduct!
                      .storehandle!,
                )
              ],
            );
          }
          return const SizedBox();
        },
      ),
      // titleWidget: Text(UserProfileBloc.getUserDetailsResponse.userDetail!.userName!??"", style: TextStyle(fontFamily: AppConstants.rRegular, fontSize: 19, fontWeight: FontWeight.w700, color: AppColors.appBlack)),
      isDefaultMenuVisible: false,
      isCustomMenuVisible: false,
      isCartVisible: true,
      customMenuButton: myMenuButton(),
    );
  }

  //endregion

  //region Menu button
  Widget myMenuButton() {
    return CupertinoButton(
        padding: EdgeInsets.zero,
        onPressed: () {
          //If deleted product then return
          if (buyerViewSingleProductBloc
              .getSingleProductDetailResponse.singleProduct!.isDeleted!) {
            return;
          }
          buyerViewSingleProductBloc.onTapDrawer(
              productReference: buyerViewSingleProductBloc
                  .getSingleProductDetailResponse
                  .singleProduct!
                  .productReference!);
        },
        child: SvgPicture.asset(AppImages.drawerIcon));
  }

  //endregion

  // region Body
  Widget body() {
    return SingleChildScrollView(
      child: ProductDetailFullCard(
        product: buyerViewSingleProductBloc.finalProduct,
        isGoToLatestVersion: widget.isGoToLatestVisible,
      ),
      // child: Column(
      //   mainAxisSize: MainAxisSize.min,
      //   mainAxisAlignment: MainAxisAlignment.start,
      //   crossAxisAlignment: CrossAxisAlignment.center,
      //   // padding: EdgeInsets.zero,
      //   children: [
      //     productAppBar(),
      //     // productAppBar(storeHandle: buyerViewSingleProductBloc.getSingleProductDetailResponse.singleProduct!.storeHandle!,
      //     //     productReference:buyerViewSingleProductBloc.getSingleProductDetailResponse.singleProduct!.productReference!
      //     // ),
      //     Container(color: AppColors.textFieldFill1, height: CommonMethods.calculateWebWidth(context: context), child: productImage()),
      //     verticalSizedBox(1),
      //     brandProduct(),
      //     ProductAvailability(product: buyerViewSingleProductBloc.finalProduct),
      //     soldAndReturns(),
      //     productLabels(),
      //     buyDetail(),
      //     // verticalSizedBox(11),
      //     // Padding(
      //     //   padding: const EdgeInsets.only(left: 16, right: 9),
      //     //   child: divider(),
      //     // ),
      //     // verticalSizedBox(10),
      //     // addComment(),
      //     // verticalSizedBox(10),
      //     // viewComment(),
      //     // verticalSizedBox(10),
      //     //Counters
      //     counts(product:buyerViewSingleProductBloc.getSingleProductDetailResponse.singleProduct! ),
      //     //Action
      //     action(product:buyerViewSingleProductBloc.getSingleProductDetailResponse.singleProduct!)
      //   ],
      // ),
    );
  }

// endregion

  //region Product appbar
  Widget productAppBar() {
    return Container(
      alignment: Alignment.centerLeft,
      padding: const EdgeInsets.all(10),
      child: Row(
        children: [
          ///Un-comment
          InkWell(
              onTap: () {
                buyerViewSingleProductBloc.goToStore(
                    storeReference: buyerViewSingleProductBloc
                        .getSingleProductDetailResponse
                        .singleProduct!
                        .storeReference!);
              },
              child: CustomImageContainer(
                width: 30,
                height: 30,
                imageUrl: buyerViewSingleProductBloc
                    .getSingleProductDetailResponse.singleProduct!.storeIcon,
                imageType: CustomImageContainerType.store,
              )),

          // ClipRRect(
          //   borderRadius: BorderRadius.circular(15),
          //   child: SizedBox(
          //       height: 30,
          //       width: 30,
          //       child: extendedImage(buyerViewSingleProductBloc.getSingleProductDetailResponse.singleProduct!.storeIcon,context,50,50,cache: true,)),
          // ),
          horizontalSizedBox(10),

          Text(
            buyerViewSingleProductBloc
                .getSingleProductDetailResponse.singleProduct!.storehandle!
                .toLowerCase(),
            style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
          ),
          Expanded(child: horizontalSizedBox(10)),
          CupertinoButton(
              padding: EdgeInsets.zero,
              onPressed: () {
                //If from add product then return
                buyerViewSingleProductBloc.onTapDrawer(
                    productReference: buyerViewSingleProductBloc
                        .getSingleProductDetailResponse
                        .singleProduct!
                        .productReference!);
              },
              child: SvgPicture.asset(AppImages.drawerIcon))
        ],
      ),
    );
  }

  //endregion

//region Product images
  Widget productImage() {
    int onScreenImageIndex = 0;

    return Stack(
      alignment: Alignment.center,
      children: [
        PageView.builder(
            allowImplicitScrolling: true,
            onPageChanged: (index) {
              onScreenImageIndex = index;
              buyerViewSingleProductBloc.onChangeSlider(index);
            },
            itemCount: buyerViewSingleProductBloc
                .getSingleProductImageResponse.data!.length,

            ///controller: buyerViewSingleProductBloc.imageSliderPageCtrl,
            itemBuilder: (context, index) {
              return GestureDetector(
                onTap: () {
                  buyerViewSingleProductBloc.goToBuyerProductImageScreen(
                      buyerViewSingleProductBloc
                          .getSingleProductImageResponse.data,
                      onScreenImageIndex);
                },
                child: buyerViewSingleProductBloc
                        .getSingleProductImageResponse.data!.isEmpty
                    ? SvgPicture.asset(AppImages.productPlaceHolder)
                    : extendedImage(
                        buyerViewSingleProductBloc.getSingleProductImageResponse
                            .data![index].productImage!,
                        context,
                        500,
                        500,
                        cache: true,
                        customPlaceHolder: AppImages.productPlaceHolder),
              );
            }),
        Positioned(
          bottom: 0,
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 10),
            child: SizedBox(
              height: 6,
              child: StreamBuilder<int>(
                  stream: buyerViewSingleProductBloc.sliderCtrl.stream,
                  initialData: 0,
                  builder: (context, snapshot) {
                    return ListView.builder(
                        itemCount: buyerViewSingleProductBloc
                            .getSingleProductImageResponse.data!.length,
                        scrollDirection: Axis.horizontal,
                        shrinkWrap: true,
                        itemBuilder: (Context, Index) {
                          return Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 5),
                            child: SvgPicture.asset(
                              AppImages.dot,
                              height: 5.29,
                              width: 5.29,
                              color: snapshot.data == Index
                                  ? AppColors.darkGray
                                  : AppColors.darkStroke,
                            ),
                          );
                        });
                  }),
            ),
          ),
        ),
        Positioned(
            top: 20,
            left: 15,
            right: 15,
            child: SizedBox(
              height: 35,
              child: Row(
                mainAxisSize: MainAxisSize.max,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  productRatings(),
                  save(),
                ],
              ),
            ))
      ],
    );
  }

//endregion

//region Product Rating
  Widget productRatings() {
    return Container();
    // return buyerViewSingleProductBloc.productList[productIndex].rating==null?SizedBox():Container(
    //
    //   height: 30,
    //   width: 60,
    //   padding: EdgeInsets.symmetric(horizontal: 5,vertical: 5),
    //   decoration: const BoxDecoration(
    //     color: AppColors.white,
    //     borderRadius: BorderRadius.all(Radius.circular(15)),
    //   ),
    //   child: Row(
    //     mainAxisSize: MainAxisSize.min,
    //     mainAxisAlignment: MainAxisAlignment.center,
    //     crossAxisAlignment: CrossAxisAlignment.center,
    //     children: [
    //       Text("${buyerViewSingleProductBloc.productList[productIndex].rating==null?0:buyerViewSingleProductBloc.productList[productIndex].rating!} ",style: TextStyle(
    //         fontSize: 16,
    //         fontWeight: FontWeight.w700,
    //         color: AppColors.yellow,
    //         fontFamily: "LatoBold",
    //       ),),
    //       Text("| ${buyerViewSingleProductBloc.productList[productIndex].countOfRatings==null?0:buyerViewSingleProductBloc.productList[productIndex].countOfRatings!}" ,style: TextStyle(
    //         fontSize: 12,
    //         fontWeight: FontWeight.w600,
    //         color: AppColors.appBlack3,
    //         fontFamily: "LatoSemibold",
    //       ),)
    //     ],
    //   ),
    // );
  }

//endregion

  //region Save
  Widget save() {
    return StreamBuilder<BuyerViewSingleProductState>(
        stream: buyerViewSingleProductBloc.saveCtrl.stream,
        builder: (context, snapshot) {
          return Visibility(
            ///Un-comment
            // visible: AppConstants.appData.storeReference==null,
            visible: false,
            child: CupertinoButton(
              padding: EdgeInsets.zero,
              onPressed: () {
                buyerViewSingleProductBloc.saveUnSaveProduct(
                    productReference: widget.productReference!);
              },
              child: buyerViewSingleProductBloc
                      .getSingleProductDetailResponse.singleProduct!.saveStatus!
                  ? SvgPicture.asset(
                      AppImages.bookmarkActive,
                      fit: BoxFit.fill,
                    )
                  : SvgPicture.asset(
                      AppImages.bookmarkInactive,
                      fit: BoxFit.fill,
                    ),
            ),
          );
        });
  }

  //endregion
//

  ///Modified
//region Brand name And Product name
  Widget brandProduct() {
    return Padding(
      padding: const EdgeInsets.only(left: 16, top: 10, bottom: 7),
      child: Align(
        alignment: Alignment.centerLeft,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                      buyerViewSingleProductBloc.getSingleProductDetailResponse
                          .singleProduct!.brandName!,
                      style: AppTextStyle.contentHeading0(
                          textColor: AppColors.appBlack)),
                  Text(
                      buyerViewSingleProductBloc.getSingleProductDetailResponse
                          .singleProduct!.productName!,
                      maxLines: 3,
                      style: AppTextStyle.contentText0(
                          textColor: AppColors.writingBlack0)),
                ],
              ),
            ),
            CupertinoButton(
                padding: const EdgeInsets.only(right: 16),
                onPressed: () {
                  buyerViewSingleProductBloc.goToBuyerProductDetail();
                },
                child: SvgPicture.asset(
                  AppImages.arrow3,
                  height: 30,
                ))
          ],
        ),
      ),
    );
  }
//endregion

  //region Selling Price and MRP
  Widget sellingMrp() {
    return Padding(
      padding: const EdgeInsets.only(left: 16, right: 16, top: 7, bottom: 7),
      child: Align(
        alignment: Alignment.centerLeft,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
                "₹ ${buyerViewSingleProductBloc.getSingleProductDetailResponse.singleProduct!.sellingPrice!}",
                style: AppTextStyle.access0(textColor: AppColors.appBlack)),
            horizontalSizedBox(17),
            Text(
                "₹ ${buyerViewSingleProductBloc.getSingleProductDetailResponse.singleProduct!.mrpPrice!}",
                style: AppTextStyle.access0(
                    textColor: AppColors.writingBlack1, isLineThrough: true)),
          ],
        ),
      ),
    );
  }

//endregion

  //region Product labels
  Widget productLabels() {
    return Container(
      margin: const EdgeInsets.only(bottom: 7),
      child: ProductLabelDropdown(
        product: buyerViewSingleProductBloc
            .getSingleProductDetailResponse.singleProduct!,
      ),
    );
  }

  //endregion

//region Store Handle
  Widget storeHandle() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      height: 27,
      child: InkWell(
        // padding: EdgeInsets.zero,
        onTap: () {
          var screen = BuyerViewStoreScreen(
              storeReference: buyerViewSingleProductBloc
                  .getSingleProductDetailResponse
                  .singleProduct!
                  .storeReference!);
          var route = MaterialPageRoute(builder: (context) => screen);
          Navigator.push(context, route);
        },
        child: Row(
          children: [
            Text(
              "Store:",
              style: AppTextStyle.contentHeading0(
                  textColor: AppColors.writingBlack1),
            ),
            horizontalSizedBox(5),
            Text(
              buyerViewSingleProductBloc
                  .getSingleProductDetailResponse.singleProduct!.storehandle!
                  .toLowerCase(),
              style:
                  AppTextStyle.contentHeading0(textColor: AppColors.brandBlack),
            )
          ],
        ),
      ),
    );
  }

//endregion

//region Product Details
  Widget productDetails() {
    return InkWell(
      onTap: () {
        buyerViewSingleProductBloc.goToBuyerProductDetail();
      },
      child: Padding(
        padding: const EdgeInsets.only(left: 16, right: 9),
        child: Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(AppStrings.productDetails.toLowerCase(),
                style: AppTextStyle.settingHeading1(
                    textColor: AppColors.appBlack)),
            SvgPicture.asset(
              AppImages.productDetailsRight,
              color: AppColors.writingColor2,
              fit: BoxFit.fill,
            )
          ],
        ),
      ),
    );
  }

//endregion

  ///New
//region Sold and returns
  Widget soldAndReturns() {
    return Visibility(
      visible: buyerViewSingleProductBloc.finalProduct.ordersCount != 0 ||
          buyerViewSingleProductBloc.finalProduct.returnCount! != 0,
      child: Padding(
        padding: const EdgeInsets.only(left: 16, right: 16, top: 7, bottom: 7),
        child: Align(
          alignment: Alignment.centerLeft,
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Visibility(
                  visible:
                      buyerViewSingleProductBloc.finalProduct.ordersCount! > 0,
                  child: Text(
                      CommonMethods.singularPluralText(
                          item: buyerViewSingleProductBloc
                              .finalProduct.ordersCount!,
                          singular: "purchase",
                          plural: "purchases"),
                      style: AppTextStyle.smallText(
                          textColor: AppColors.brandBlack))),
              const SizedBox(
                width: 5,
              ),
              Visibility(
                  visible:
                      buyerViewSingleProductBloc.finalProduct.returnCount! > 0,
                  child: Text(
                      CommonMethods.singularPluralText(
                          item: buyerViewSingleProductBloc
                              .finalProduct.returnCount!,
                          singular: "return",
                          plural: "returns"),
                      style:
                          AppTextStyle.smallText(textColor: AppColors.orange))),

              // Text("₹ ${buyerViewSingleProductBloc.finalProduct.sellingPrice!}", style: AppTextStyle.access0(textColor: AppColors.appBlack)),
              // horizontalSizedBox(17),
              // Text("₹ ${buyerViewSingleProductBloc.finalProduct.mrpPrice!}",
              //     style: AppTextStyle.access0(textColor: AppColors.writingBlack1, isLineThrough: true)),
            ],
          ),
        ),
      ),
    );
  }

//endregion

//region Buy Now and View detail
  Widget buyDetail() {
    // Retrieve the data from the Shopping cart data model
    ShoppingCartQuantityDataModel shoppingCartQuantityDataModel =
        Provider.of<ShoppingCartQuantityDataModel>(context);
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 18),
      child: Row(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(
            child: Container(
              decoration: const BoxDecoration(
                  borderRadius: BorderRadius.all(Radius.circular(10))),
              child: StreamBuilder<bool>(
                  stream: buyerViewSingleProductBloc.buyNowAddCartCtrl.stream,
                  initialData: false,
                  builder: (context, snapshot) {
                    ///If product contain version
                    // if (widget.isGoToLatestVisible!) {
                    //   return ProductCommonWidgets.buyAddAndGoToButton(
                    //       product: buyerViewSingleProductBloc.finalProduct,
                    //       buttonName: AppStrings.goToLatestVersion,
                    //       onTap: () {
                    //         buyerViewSingleProductBloc.goToSingleProductScreen(productReference: widget.productReference!);
                    //       });
                    // }
//
                    return BuyButton(
                      product: buyerViewSingleProductBloc
                          .getSingleProductDetailResponse.singleProduct!,
                      isFromAddProduct: false,
                      isGoToLatestVersion: false,
                    );

                    // ///If seller viewing own product
                    // if(AppConstants.appData.storeReference == buyerViewSingleProductBloc.getSingleProductDetailResponse.singleProduct!.storeReference){
                    //   return ProductCommonWidgets.buyButton(
                    //       buttonName: AppStrings.updateStock,
                    //
                    //       onTap: (){
                    //        buyerViewSingleProductBloc.onTapUpdateStock(product:buyerViewSingleProductBloc.getSingleProductDetailResponse.singleProduct! );
                    //       }
                    //   );
                    // }
                    // ///If Store view other store
                    // if(AppConstants.appData.isStoreView! && AppConstants.appData.storeReference! != buyerViewSingleProductBloc.getSingleProductDetailResponse.singleProduct!.storeReference){
                    //   return ProductCommonWidgets.buyButton(
                    //       buttonName: AppStrings.switchToBuyer,
                    //       onTap:(){
                    //         buyerViewSingleProductBloc.switchToBuyer(productReference:buyerViewSingleProductBloc.getSingleProductDetailResponse.singleProduct!.productReference!);
                    //       }
                    //   );
                    //
                    //   return CupertinoButton(
                    //
                    //     borderRadius: BorderRadius.circular(150),
                    //
                    //     color: AppColors.brandGreen,
                    //     padding: const EdgeInsets.symmetric(horizontal: 20),
                    //     onPressed:AppConstants.appData.storeReference! == buyerViewSingleProductBloc.getSingleProductDetailResponse.singleProduct!.storeReference?(){
                    //     }:null,
                    //
                    //     child: Center(
                    //       child: Text("Buy now",
                    //         style:AppTextStyle.button2Bold(textColor: AppColors.appWhite)
                    //         ,),
                    //     ),
                    //
                    //
                    //   );
                    // }
                    // ///Of same product already in card
                    // if(shoppingCartQuantityDataModel.productReferenceList.contains(buyerViewSingleProductBloc.getSingleProductDetailResponse.singleProduct!.productReference!)){
                    //   return ProductCommonWidgets.buyButton(
                    //       buttonName: AppStrings.goToCart,
                    //
                    //       onTap: (){
                    //         buyerViewSingleProductBloc.gotoShoppingCart();
                    //       }
                    //   );
                    //
                    //   return CupertinoButton(
                    //     borderRadius: BorderRadius.circular(150),
                    //     color: AppColors.brandGreen,
                    //     padding: const EdgeInsets.symmetric(horizontal: 20),
                    //     child:  Center(
                    //       child: Text(
                    //         "Go to cart",
                    //           style:AppTextStyle.button2Bold(textColor: AppColors.appWhite)
                    //       ),
                    //     ),
                    //     onPressed: () {
                    //       buyerViewSingleProductBloc.gotoShoppingCart();
                    //
                    //       // buyerViewSingleProductBloc.onTapBuyAddCart(buyerViewSingleProductBloc.storeProductResponse.data.[]);
                    //     },
                    //   );
                    // }
                    // ///If cat is not empty
                    // if(shoppingCartQuantityDataModel.productReferenceList.isNotEmpty){
                    //   return  CupertinoButton(
                    //       borderRadius: BorderRadius.circular(150),
                    //       color: AppColors.brandGreen,
                    //       padding: EdgeInsets.zero,
                    //       onPressed: buyerViewSingleProductBloc.getSingleProductDetailResponse.singleProduct!.inStock!=0?() async {
                    //         await buyerViewSingleProductBloc.addToCart(goToCart: false);
                    //         // buyerViewSingleProductBloc.onTapBuyAddCart(buyerViewSingleProductBloc.storeProductResponse.data.[]);
                    //       }:null,
                    //       child: Container(
                    //         decoration: const BoxDecoration(borderRadius: BorderRadius.all(Radius.circular(150))),
                    //
                    //         padding: const EdgeInsets.symmetric(horizontal: 20,vertical: 10),
                    //
                    //         child: Text(
                    //             AppStrings.addToCart,
                    //           style:AppTextStyle.button2Bold(textColor:buyerViewSingleProductBloc.getSingleProductDetailResponse.singleProduct!.inStock!=0?AppColors.appWhite:AppColors.lightGray                          ),
                    //       )
                    //   ));
                    // }
                    // ///Buy
                    // return  CupertinoButton(
                    //     borderRadius: BorderRadius.circular(150),
                    //     color: AppColors.brandGreen,
                    //     padding: EdgeInsets.zero,
                    //     onPressed: buyerViewSingleProductBloc.getSingleProductDetailResponse.singleProduct!.inStock!=0?() async {
                    //       await buyerViewSingleProductBloc.addToCart(goToCart: true);
                    //       // buyerViewSingleProductBloc.onTapBuyAddCart(buyerViewSingleProductBloc.storeProductResponse.data.[]);
                    //     }:null,
                    //     child: Container(
                    //       decoration: const BoxDecoration(borderRadius: BorderRadius.all(Radius.circular(150))),
                    //
                    //       padding: const EdgeInsets.symmetric(horizontal: 20,vertical: 10),
                    //
                    //       child: Text(
                    //           AppStrings.buyNow,
                    //           style:AppTextStyle.button2Bold(textColor:buyerViewSingleProductBloc.getSingleProductDetailResponse.singleProduct!.inStock!=0?AppColors.appWhite:AppColors.lightGray)
                    //       ),
                    //     )
                    // );
                  }),
            ),
          ),
          //Make it visible only if product belongs to logged in store
          //Is not from add product and store reference has to be same as logged in store reference
          Visibility(
            visible: buyerViewSingleProductBloc.finalProduct.storeReference! ==
                AppConstants.appData.storeReference,
            child: CupertinoButton(
                padding: EdgeInsets.zero,
                onPressed: () {
                  buyerViewSingleProductBloc.onTapEditDetails(
                      productReference: buyerViewSingleProductBloc
                          .finalProduct.productReference!,
                      storeId:
                          buyerViewSingleProductBloc.finalProduct.storeid!);
                },
                child: Icon(
                  Icons.edit,
                  color: AppColors.appBlack,
                )),
          )
          // horizontalSizedBox(10),
          //
          // ///Hide if seller view and product version is smaller then current version
          // Visibility(
          //   visible: !widget.isGoToLatestVisible!,
          //   child: ProductCommonWidgets.buyButton(
          //       product: buyerViewSingleProductBloc.finalProduct,
          //
          //       buttonColor: AppColors.appWhite,
          //       buttonName:
          //           buyerViewSingleProductBloc.getSingleProductDetailResponse.singleProduct!.storeReference == AppConstants.appData.storeReference
          //               ? AppStrings.editDetails
          //               : AppStrings.viewDetails,
          //       borderColor: AppColors.darkGray,
          //       textColor: AppColors.appBlack,
          //       onTap: () {
          //         //onTapEditDetails
          //         buyerViewSingleProductBloc.getSingleProductDetailResponse.singleProduct!.storeReference == AppConstants.appData.storeReference
          //             ? buyerViewSingleProductBloc.onTapEditDetails(
          //                 productReference: buyerViewSingleProductBloc.getSingleProductDetailResponse.singleProduct!.productReference!,
          //                 storeId: buyerViewSingleProductBloc.getSingleProductDetailResponse.singleProduct!.storeid!)
          //             : buyerViewSingleProductBloc.goToBuyerProductDetail();
          //       }),
          // ),
          // Expanded(child: horizontalSizedBox(50)),
          // CupertinoButton(
          //     padding: EdgeInsets.zero,
          //     onPressed: () {
          //       buyerViewSingleProductBloc.onTapShare(
          //           imageUrl: buyerViewSingleProductBloc.getSingleProductDetailResponse.singleProduct!.prodImages!.first.productImage);
          //     },
          //     child: SvgPicture.asset(
          //       AppImages.shareIcon,
          //       color: AppColors.brandGreen,
          //     ))
        ],
      ),
    );
  }

//endregion

//region View All Comment
//   Widget viewComment() {
//     return InkWell(
//       onTap: () {
//         buyerViewSingleProductBloc.viewComment(
//             productRef: buyerViewSingleProductBloc.getSingleProductDetailResponse.singleProduct!.productReference!,
//             productId: buyerViewSingleProductBloc.getSingleProductDetailResponse.singleProduct!.productid!,
//             storeReference: buyerViewSingleProductBloc.getSingleProductDetailResponse.singleProduct!.storeReference!,
//             isWriteComment: false);
//       },
//       child: Padding(
//         padding: const EdgeInsets.symmetric(horizontal: 16),
//         child: Row(
//           children: [
//             Text("View comments", textAlign: TextAlign.left, style: AppTextStyle.contentText0(textColor: AppColors.writingBlack0)),
//             Expanded(child: horizontalSizedBox(10)),
//           ],
//         ),
//       ),
//     );
//   }

//endregion

//region Add Comment
  Widget addComment() {
    return InkWell(
      onTap: () {
        buyerViewSingleProductBloc.addComment(
            productRef: buyerViewSingleProductBloc
                .getSingleProductDetailResponse
                .singleProduct!
                .productReference!,
            productId: buyerViewSingleProductBloc
                .getSingleProductDetailResponse.singleProduct!.productid!,
            storeReference: buyerViewSingleProductBloc
                .getSingleProductDetailResponse.singleProduct!.storeReference!,
            isWriteComment: true);
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 10),
        // height: 43,
        decoration: BoxDecoration(
          color: AppColors.textFieldFill1,
        ),
        child: Row(
          children: [
            SvgPicture.asset(
              AppImages.emoji,
              fit: BoxFit.contain,
            ),
            Expanded(
              child: TextFormField(
                enabled: false,
                readOnly: true,
                maxLines: 1,
                decoration: InputDecoration(
                  // prefixIcon: SvgPicture.asset(AppImages.emoji,fit:BoxFit.contain,),
                  filled: true,
                  contentPadding:
                      const EdgeInsets.symmetric(vertical: 10, horizontal: 10),
                  fillColor: AppColors.textFieldFill1,
                  isDense: true,
                  hintText: AppStrings.commentHint,
                  hintStyle: AppTextStyle.contentText0(
                      textColor: AppColors.writingBlack1),
                  border: InputBorder.none,
                  // focusedBorder: OutlineInputBorder(
                  //     borderRadius: BorderRadius.circular(22),
                  //     borderSide: BorderSide.none
                  //
                  // ),
                  // enabledBorder: OutlineInputBorder(
                  //
                  //     borderRadius: BorderRadius.circular(22),
                  //     borderSide: BorderSide.none
                  // ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

//endregion

//region Down Arrow
  Widget downArrow() {
    return Center(
      child: SvgPicture.asset(
        AppImages.downArrow,
        height: 7,
        color: AppColors.appBlack,
        width: 13,
      ),
    );
  }

//endregion

  //region Counts
  Widget counts({required Product product}) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 15, vertical: 5),
      child: Row(
        children: [
          Visibility(
            visible: product.likeCount != 0,
            child: Container(
                margin: const EdgeInsets.only(right: 5),
                child: Text(
                  "${product.likeCount} ${product.likeCount == 1 ? "like" : "likes"}",
                  style: AppTextStyle.smallText(
                      textColor: AppColors.writingBlack0),
                )),
          ),
          Visibility(
            visible: product.commentCount != 0,
            child: Container(
                margin: const EdgeInsets.only(right: 5),
                child: Text(
                    "${product.commentCount} ${product.commentCount == 1 ? "comment" : "comments"}",
                    style: AppTextStyle.smallText(
                        textColor: AppColors.writingBlack0))),
          ),
          // const Expanded(child: SizedBox()),
          // Container(margin: EdgeInsets.only(right: 5), child: Text("710 reposts", style: AppTextStyle.smallText(textColor: AppColors.writingBlack1))),
          // Container(margin: EdgeInsets.only(right: 5), child: Text("50 shares", style: AppTextStyle.smallText(textColor: AppColors.writingBlack1))),
          //
        ],
      ),
    );
  }

  //endregion

  //region Action
  Widget action({required Product product}) {
    return Container(
      margin: const EdgeInsets.only(bottom: 10),
      padding: const EdgeInsets.symmetric(vertical: 3, horizontal: 15),
      child: Row(
        children: [
          //Like
          Container(
            margin: const EdgeInsets.only(right: 10),
            height: 26,
            width: 26,
            child: CupertinoButton(
              padding: EdgeInsets.zero,
              onPressed: () {
                buyerViewSingleProductBloc.onTapHeart(product: product);
                // widget.onTapHeart();
              },
              child: SvgPicture.asset(
                fit: BoxFit.fill,
                product.likeStatus!
                    ? AppImages.postLike
                    : AppImages.postDisLike,
                color: product.likeStatus! ? AppColors.red : AppColors.appBlack,
              ),
            ),
          ),

          //Comment
          Container(
            margin: const EdgeInsets.only(right: 10),
            height: 26,
            width: 26,
            child: CupertinoButton(
              padding: EdgeInsets.zero,
              onPressed: () {
                buyerViewSingleProductBloc.viewComment(
                    productRef: product.productReference!,
                    storeReference: product.storeReference!);
              },
              child: SvgPicture.asset(AppImages.postComment,
                  color: AppColors.appBlack),
            ),
          ),

          //Save
          AppToolTip(
            message: AppStrings.thisFeatureIsCommingSoon,
            toolTipWidget: Opacity(
              opacity: 0.2,
              child: SizedBox(
                  height: 26,
                  width: 26,
                  child: SvgPicture.asset(AppImages.savePost,
                      color: AppColors.appBlack)),
            ),
          ),
          const Expanded(child: SizedBox()),
          //Repost
          AppToolTip(
            message: AppStrings.thisFeatureIsCommingSoon,
            toolTipWidget: Opacity(
              opacity: 0.2,
              child: SizedBox(
                height: 26,
                width: 26,
                child: SvgPicture.asset(AppImages.repost,
                    color: AppColors.appBlack),
              ),
            ),
          ),
          const SizedBox(
            width: 10,
          ),
          //Share
          SizedBox(
            height: 26,
            width: 26,
            child: CupertinoButton(
                padding: EdgeInsets.zero,
                onPressed: () {
                  buyerViewSingleProductBloc.onTapShare(
                    imageUrl: product.prodImages!.isEmpty
                        ? null
                        : product.prodImages!.first.productImage,
                  );
                },
                child: SvgPicture.asset(
                  AppImages.sharePost,
                  color: AppColors.appBlack,
                )),
          ),
        ],
      ),
    );
  }
//endregion
}
