import 'dart:async';
import 'package:flutter/material.dart';
import 'package:swadesic/features/seller/seller_store/store_dashboard/dashboard_card_slider/dashboard_card_slider_bloc.dart';
import 'package:swadesic/model/store_dashboard_response/store_dashboard_response.dart';
import 'package:swadesic/util/app_colors.dart';

class DashboardCardSlider extends StatefulWidget {
  final StoreDashBoard storeDashboard;
  final String storeReference;

  const DashboardCardSlider({
    Key? key,
    required this.storeDashboard,
    required this.storeReference,
  }) : super(key: key);

  @override
  State<DashboardCardSlider> createState() => _DashboardCardSliderState();
}

class _DashboardCardSliderState extends State<DashboardCardSlider> {
  late DashboardCardSliderBloc bloc;
  PageController? _pageController;
  Timer? _autoScrollTimer;
  int _currentPage = 0;
  bool _isAutoScrolling = false;
  // Toggle to enable/disable auto-scroll. Disabled by default.
  static const bool _kEnableAutoScroll = false;

  @override
  void initState() {
    super.initState();
    bloc = DashboardCardSliderBloc(context, widget.storeReference);
    _initializeSlider();
  }

  @override
  void dispose() {
    _autoScrollTimer?.cancel();
    _pageController?.dispose();
    bloc.dispose();
    super.dispose();
  }

  void _initializeSlider() {
    _pageController = PageController();
    // Start auto-scroll after a short delay (disabled by default)
    if (_kEnableAutoScroll) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _startAutoScroll();
      });
    }
  }

  void _startAutoScroll() {
    _autoScrollTimer?.cancel();

    _autoScrollTimer = Timer.periodic(const Duration(seconds: 5), (timer) {
      if (!mounted || _pageController == null || !_pageController!.hasClients) {
        return;
      }

      final visibleCards = bloc.getVisibleCards(widget.storeDashboard);
      if (visibleCards.isEmpty) return;

      final nextPage = (_currentPage + 1) % visibleCards.length;
      _isAutoScrolling = true;

      _pageController!.animateToPage(
        nextPage,
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeInOut,
      );
    });
  }

  void _stopAutoScroll() {
    _autoScrollTimer?.cancel();
  }

  void _onPageChanged(int page) {
    setState(() {
      _currentPage = page;
      _isAutoScrolling = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    final visibleCards = bloc.getVisibleCards(widget.storeDashboard);

    if (visibleCards.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 10, vertical: 2),
      height: 100,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: AppColors.textFieldFill1,
      ),
      child: NotificationListener<ScrollNotification>(
        onNotification: (ScrollNotification notification) {
          if (notification is ScrollStartNotification && !_isAutoScrolling) {
            // Stop auto-scroll when user starts swiping
            _stopAutoScroll();
          }
          return false;
        },
        child: PageView.builder(
          controller: _pageController,
          onPageChanged: _onPageChanged,
          itemCount: visibleCards.length,
          itemBuilder: (context, index) {
            return GestureDetector(
              onTap: visibleCards[index].onTap,
              child: Padding(
                padding: const EdgeInsets.only(
                    left: 10.0, right: 10.0, top: 10.0, bottom: 10.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Title and dots row
                    Row(
                      children: [
                        Expanded(
                          child: visibleCards[index].titleWidget,
                        ),
                        _buildDotsIndicator(visibleCards.length, _currentPage),
                      ],
                    ),
                    const SizedBox(height: 8),
                    // Content
                    Expanded(
                      child: visibleCards[index].contentWidget,
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildDotsIndicator(int totalCards, int currentIndex) {
    return Row(
      children: List.generate(totalCards, (index) {
        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 2),
          width: 6,
          height: 6,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: index == currentIndex
                ? AppColors.appBlack
                : AppColors.borderColor1,
          ),
        );
      }),
    );
  }
}

class DashboardCard {
  final Widget titleWidget;
  final Widget contentWidget;
  final VoidCallback? onTap;

  DashboardCard({
    required this.titleWidget,
    required this.contentWidget,
    this.onTap,
  });
}
