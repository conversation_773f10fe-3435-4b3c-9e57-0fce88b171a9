import 'package:expandable/expandable.dart';
import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/components/new_return_components/return_completed/return_completed_bloc.dart';
import 'package:swadesic/features/seller/seller_all_orders/seller_all_orders_common_widget.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/buyer_sub_order/buyer_sub_order_bloc.dart';
import 'package:swadesic/model/order_response/get_order_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/model/order_response/sub_order.dart';

class ReturnCompletedScreen extends StatefulWidget {
  final List<SubOrder> suborderList;
  final BuyerSubOrderBloc buyerSubOrderBloc;
  final Order order;
  const ReturnCompletedScreen(
      {Key? key,
      required this.suborderList,
      required this.buyerSubOrderBloc,
      required this.order})
      : super(key: key);

  @override
  State<ReturnCompletedScreen> createState() => _ReturnCompletedScreenState();
}

class _ReturnCompletedScreenState extends State<ReturnCompletedScreen> {
  // region Bloc
  late ReturnCompletedBloc returnCompletedBloc;

  // endregion
  // region Init
  @override
  void initState() {
    returnCompletedBloc = ReturnCompletedBloc(
        context, widget.buyerSubOrderBloc, widget.order, widget.suborderList);
    returnCompletedBloc.init();
    super.initState();
  }

  // endregion
  //region Dis update
  @override
  void didUpdateWidget(covariant ReturnCompletedScreen oldWidget) {
    returnCompletedBloc = ReturnCompletedBloc(
        context, widget.buyerSubOrderBloc, widget.order, widget.suborderList);
    returnCompletedBloc.init();
    super.didUpdateWidget(oldWidget);
  }
  //endregion

  //region Build

  //region Build
  @override
  Widget build(BuildContext context) {
    return body();
  }
  //endregion

  //region Body
  Widget body() {
    return ListView.builder(
        physics: const NeverScrollableScrollPhysics(),
        shrinkWrap: true,
        itemCount: returnCompletedBloc.groupNameList.length,
        itemBuilder: (context, index) {
          return returnCompleted(
              groupName: returnCompletedBloc.groupNameList[index]);
        });
  }
  //endregion

  //region Shipping in progress heading and all
  Widget returnCompleted({required String groupName}) {
    List<SubOrder> groupedSuborderList = [];
    // String packageNumber = "";
    ///Add all suborders to the suborder list as per the display package number
    groupedSuborderList = returnCompletedBloc.subOrderList
        .where((element) => element.displayPackageNumber == groupName)
        .toList();

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 30, horizontal: 10),
      decoration: BoxDecoration(
          color: AppColors.appWhite,
          border: Border(bottom: BorderSide(color: AppColors.lightStroke))),
      child: ExpandablePanel(
        //region Theme
        theme: ExpandableThemeData(
          animationDuration: Duration(microseconds: 100),
          iconPlacement: ExpandablePanelIconPlacement.right,
          // alignment: Alignment.bottomRight
          tapBodyToCollapse: true,
          tapHeaderToExpand: true,
          tapBodyToExpand: true,
          iconPadding: EdgeInsets.zero,
          //useInkWell: false,
          // iconRotationAngle:math.pi/2,
          headerAlignment: ExpandablePanelHeaderAlignment.center,

          iconSize: 40,
          iconColor: AppColors.appBlack,

          // iconPadding: EdgeInsets.symmetric( horizontal: 10),
          //iconColor: Colors.green
        ),
        //endregion

        //Waiting for confirmation
        //region Header
        header:
            header(headerOrderList: groupedSuborderList, groupName: groupName),
        //endregion
        collapsed: needHelp(groupedSubOrders: groupedSuborderList),

        expanded: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            needHelp(groupedSubOrders: groupedSuborderList),
            Container(
              padding: const EdgeInsets.only(top: 30),
              color: AppColors.appWhite,
              child: ListView.builder(
                  physics: const NeverScrollableScrollPhysics(),
                  shrinkWrap: true,
                  itemCount: groupedSuborderList.length,
                  itemBuilder: (context, index) {
                    return Column(
                      children: [
                        Column(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            productInfoCard(
                                context: context,
                                subOrder: groupedSuborderList[index]),
                          ],
                        ),
                        //Divider
                        Visibility(
                          visible: groupedSuborderList.length - 1 != index,
                          child: Container(
                            color: AppColors.appWhite,
                            child: Padding(
                              padding: const EdgeInsets.symmetric(vertical: 10),
                              child: divider(),
                            ),
                          ),
                        ),
                      ],
                    );
                  }),
            ),
          ],
        ),
      ),
    );
  }

//endregion

  //region Header
  Widget header(
      {required List<SubOrder> headerOrderList, required String groupName}) {
    return SellerAllOrdersCommonWidgets.sellerCommonComponent(
      icon: AppImages.packageIcon,
      componentName: "Return Completed :$groupName",
      suborderList: headerOrderList,
      isSellerSideDelivered: false,
      additionalWidgets: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            alignment: Alignment.centerLeft,
            child: Text(
              "Product has been returned to the seller, refund will be initiated shortly",
              style:
                  AppTextStyle.contentHeading0(textColor: AppColors.brandBlack),
            ),
          ),
        ],
      ),
      isSellerSideReturnedDateShow: true,
      isSellerSideReturned: true,
      isEstimateDeliveryShow: false,
    );
  }

  //endregion

  //region Need help
  Widget needHelp({required List<SubOrder> groupedSubOrders}) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Expanded(
            child: AppCommonWidgets.inActiveButton(
                buttonName: AppStrings.needHelp,
                onTap: () {
                  CommonMethods.reportAndSuggestion(context: context);
                })),
      ],
    );
  }
//endregion

//region Divider
  Widget divider() {
    return Divider(
      color: AppColors.lightGray,
      height: 1,
      thickness: 1,
    );
  }
//endregion

//region Product Info Card
  Widget productInfoCard(
      {required BuildContext context, required SubOrder subOrder}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 10),
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 10),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  //Product name
                  ///Brand and product name
                  RichText(
                    textScaler: MediaQuery.textScalerOf(
                        AppConstants.globalNavigator.currentContext!),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    text: TextSpan(
                      children: [
                        TextSpan(
                          text: subOrder.productBrand,
                          style: AppTextStyle.contentHeading0(
                            textColor: AppColors.appBlack,
                          ),
                        ),
                        TextSpan(
                            text: " ${subOrder.productName}",
                            style: AppTextStyle.contentText0(
                              textColor: AppColors.appBlack,
                            )),
                      ],
                    ),
                  ),
                  verticalSizedBox(5),
                  //Price
                  Text(
                    "₹${subOrder.sellingPrice} X ${subOrder.productQuantity} = ₹${subOrder.sellingPrice! * subOrder.productQuantity!}"
                    "${subOrder.suborderFeeDetails!.productLevelDeliveryFee! == 0 ? "" : "    Delivery: ₹${subOrder.suborderFeeDetails!.productLevelDeliveryFee!}"}",
                    style: AppTextStyle.contentHeading0(
                      textColor: AppColors.appBlack,
                    ),
                  ),
                  // Return reason if available
                  if (subOrder.returnReason != null &&
                      subOrder.returnReason!.isNotEmpty)
                    Padding(
                      padding: const EdgeInsets.only(top: 5),
                      child: Text(
                        "Return reason: ${subOrder.returnReason}",
                        style: AppTextStyle.heading3Regular(
                          textColor: AppColors.orange,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                ],
              ),
            ),
            horizontalSizedBox(20),
            //Product image
            ClipRRect(
                borderRadius: const BorderRadius.all(Radius.circular(10)),
                child: Container(
                    color: AppColors.textFieldFill1,
                    height: 60,
                    width: 60,
                    child: extendedImage(
                      "${subOrder.displayProductImage}",
                      customPlaceHolder: AppImages.productPlaceHolder,
                      context,
                      100,
                      100,
                      cache: true,
                    )))
          ],
        ),
      ),
    );
  }
//endregion

//region Update delivery status and tracking detail
// Widget confirmDeliveryStatusAndTrackingDetail({required List<SubOrder> subOrders,required String packageNumber}){
//   return Container(
//     color: AppColors.white,
//     child: Padding(
//       padding: const EdgeInsets.symmetric(horizontal: 10),
//       child: Row(
//         mainAxisSize: MainAxisSize.max,
//         mainAxisAlignment: MainAxisAlignment.start,
//         crossAxisAlignment: CrossAxisAlignment.center,
//         children: [
//           sellerAllOrderActionButton(buttonName: "Update delivery status",onPress: (){
//             // sellerDeliveredBloc.onTapDeliveryStatus(subOrders, sellerDeliveredBloc,subOrders.first.selfDeliveryByStore!,widget.order, packageNumber:packageNumber);
//             sellerDeliveredBloc.onTapDeliveryStatus();
//           }),
//           horizontalSizedBox(10),
//           sellerAllOrderCancelButton(buttonName: "Tracking details",onPress:(){
//             // sellerDeliveredBloc.onTapTrackingDetail(subOrders, sellerDeliveredBloc,subOrders.first.selfDeliveryByStore!,widget.order);
//             sellerDeliveredBloc.onTapTrackingDetail();
//           } ),
//         ],
//       ),
//     ),
//   );
// }
//endregion
}
