import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/buyer_sub_order/buyer_sub_order_bloc.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/components/delivered_successfully/rate_review/edit_review/edit_review.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/components/delivered_successfully/rate_review/rate_review_bloc.dart';
import 'package:swadesic/model/order_response/get_order_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/model/order_response/sub_order.dart';

class RateReview extends StatefulWidget {
  final List<SubOrder> subOrderList;
  final BuyerSubOrderBloc buyerSubOrderBloc;
  final Order order;

  const RateReview(
      {Key? key,
      required this.subOrderList,
      required this.buyerSubOrderBloc,
      required this.order})
      : super(key: key);

  @override
  State<RateReview> createState() => _RateReviewState();
}

class _RateReviewState extends State<RateReview> {
  // region Bloc
  late RateReviewBloc rateReviewBloc;

  // endregion

  // region Init
  @override
  void initState() {
    rateReviewBloc = RateReviewBloc(
        context, widget.subOrderList, widget.buyerSubOrderBloc, widget.order);
    rateReviewBloc.init();
    super.initState();
  }

  // endregion

  @override
  Widget build(BuildContext context) {
    return productDropDown();
  }

  //region Product dropdown
  Widget productDropDown() {
    return StreamBuilder<RateReviewState>(
        stream: rateReviewBloc.rateReviewStateCtrl.stream,
        initialData: RateReviewState.Loading,
        builder: (context, snapshot) {
          //Loading
          if (snapshot.data == RateReviewState.Loading) {
            return AppCommonWidgets.appCircularProgress();
          }
          //Success
          if (snapshot.data == RateReviewState.Success) {
            return SingleChildScrollView(
              child: Column(
                children: [
                  ///Drop down
                  InkWell(
                    onTap: () {
                      rateReviewBloc.onTapProductList();
                    },
                    child: Container(
                      color: AppColors.lightWhite3,
                      padding: const EdgeInsets.all(10),
                      child: Row(
                        children: [
                          //Grand total
                          Expanded(
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  "Delivered products",
                                  style: AppTextStyle.sectionHeading(
                                      textColor: AppColors.appBlack),
                                ),
                                Text(
                                  "Reviews are shown in product comment section to help other users get informed",
                                  style: AppTextStyle.sectionHeading(
                                      textColor: AppColors.brandBlack),
                                )
                              ],
                            ),
                          ),
                          horizontalSizedBox(10),
                          rateReviewBloc.isProductListDropDownVisible
                              ? RotatedBox(
                                  quarterTurns: 2,
                                  child: SvgPicture.asset(AppImages.downArrow),
                                )
                              : SvgPicture.asset(AppImages.downArrow)
                        ],
                      ),
                    ),
                  ),
                  verticalSizedBox(10),

                  ///List
                  Visibility(
                      visible: rateReviewBloc.isProductListDropDownVisible,
                      child: subOrderList()),
                  verticalSizedBox(30),
                  // Visibility(
                  //     visible: rateReviewBloc.noReviewSubOrders.isNotEmpty,
                  //     child: addYourReview()),

                  //feedBack(),
                  AppCommonWidgets.bottomListSpace(context: context),
                ],
              ),
            );
          }
          return const SizedBox();
        });
  }

//endregion

//region Sub orders list
  Widget subOrderList() {
    return Container(
      margin: const EdgeInsets.all(10),
      decoration: BoxDecoration(
          border: Border.all(
            color: AppColors.lightGray2,
            width: 1,
          ),
          borderRadius: const BorderRadius.all(Radius.circular(10))),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          //Not have review
          ListView.builder(
              physics: const NeverScrollableScrollPhysics(),
              itemCount: rateReviewBloc.noReviewSubOrders.length,
              shrinkWrap: true,
              itemBuilder: (buildContext, index) {
                return productDetailAndRating(
                    subOrder: rateReviewBloc.noReviewSubOrders[index]);
              }),
          //Have review
          ListView.builder(
              physics: const NeverScrollableScrollPhysics(),
              itemCount: rateReviewBloc.alreadyHaveReviewSubOrders.length,
              shrinkWrap: true,
              itemBuilder: (buildContext, index) {
                return EditReview(
                  order: rateReviewBloc.order,
                  subOrderList: [
                    rateReviewBloc.alreadyHaveReviewSubOrders[index]
                  ],
                  buyerSubOrderBloc: rateReviewBloc.buyerSubOrderBloc,
                );
              }),
        ],
      ),
    );
  }

//endregion

//region Product detail and rating
  Widget productDetailAndRating({required SubOrder subOrder}) {
    final TextEditingController comment =
        TextEditingController(text: subOrder.reviewComment ?? "");
    return Container(
      padding: const EdgeInsets.only(top: 25, left: 10, right: 10, bottom: 25),
      child: Column(
        children: [
          ///Image and name
          Row(
            children: [
              ClipRRect(
                borderRadius: const BorderRadius.all(Radius.circular(6)),
                child: Container(
                  height: 50,
                  width: 50,
                  decoration: const BoxDecoration(
                      borderRadius: BorderRadius.all(Radius.circular(6))),
                  child: extendedImage(
                    subOrder.displayProductImage!,
                    context,
                    100,
                    100,
                    cache: true,
                  ),
                ),
              ),
              horizontalSizedBox(20),
              Expanded(
                  child: Padding(
                padding: const EdgeInsets.only(right: 20),
                child: appText(subOrder.productName!,
                    color: AppColors.writingBlack,
                    fontWeight: FontWeight.w600,
                    fontFamily: AppConstants.rRegular,
                    fontSize: 14,
                    maxLine: 2),
              )),
            ],
          ),
          verticalSizedBox(10),

          ///Ratting and add image
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 5),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                RatingBar.builder(
                  initialRating: subOrder.ratingValue,
                  minRating: 0,
                  direction: Axis.horizontal,
                  glowColor: AppColors.yellow,
                  unratedColor: AppColors.lightGray,
                  allowHalfRating: false,
                  itemCount: 5,
                  //itemPadding: const EdgeInsets.symmetric(horizontal:2),
                  itemBuilder: (context, _) => const Icon(
                    Icons.star,
                    color: Colors.amber,
                  ),
                  onRatingUpdate: (rating) {
                    subOrder.ratingValue = rating;
                    //If rating has not added then show user to add rating first
                    if (rating == 0.0) {
                      subOrder.ratingValue = 0.0;
                      subOrder.reviewComment = "";
                      subOrder.reviewImages.clear();
                      //refresh
                      rateReviewBloc.rateReviewStateCtrl.sink
                          .add(RateReviewState.Success);
                      //Close keyboard
                      CommonMethods.closeKeyboard(context);
                    }
                    //buyerProductCommentBloc.productRating = rating.round();
                    // //print(buyerProductCommentBloc.productRating);
                  },
                ),
                Expanded(child: horizontalSizedBox(10)),
                //Add image
                InkWell(
                  onTap: () {
                    //If rating has not added then show user to add rating first
                    if (subOrder.ratingValue == 0.0) {
                      return CommonMethods.toastMessage(
                          AppStrings.pleaseRateTheProductFirst, context);
                    }
                    rateReviewBloc.goToBuyerAddImageScreen(subOrder: subOrder);
                  },
                  child: Container(
                    alignment: Alignment.center,
                    padding:
                        const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                    decoration: BoxDecoration(
                        borderRadius:
                            const BorderRadius.all(Radius.circular(10)),
                        border: Border.all(color: AppColors.lightGray)),
                    child: Text(
                      "add images",
                      style: TextStyle(
                          fontFamily: AppConstants.rRegular,
                          fontWeight: FontWeight.w600,
                          fontSize: 15,
                          color: AppColors.writingColor3),
                    ),
                  ),
                ),
              ],
            ),
          ),

          ///Review images
          reviewImages(subOrder: subOrder),

          verticalSizedBox(20),
          Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Expanded(
                child: colorFilledTextField(
                  onTapTextField: () {
                    //If rating has not added then show user to add rating first
                    if (subOrder.ratingValue == 0.0) {
                      //Close keyboard
                      CommonMethods.closeKeyboard(context);
                      return CommonMethods.toastMessage(
                          AppStrings.pleaseRateTheProductFirst, context);
                    }
                  },
                  context: context,
                  textFieldCtrl: comment,
                  hintText: "write your review..",
                  hintFontSize: 14,
                  textFieldMaxLine: 1,
                  onChangeText: () {
                    //If rating has not added then show user to add rating first
                    if (subOrder.ratingValue == 0.0) {
                      //Close keyboard
                      CommonMethods.closeKeyboard(context);
                      return CommonMethods.toastMessage(
                          AppStrings.pleaseRateTheProductFirst, context);
                    }

                    subOrder.reviewComment = comment.text;
                  },
                  keyboardType: TextInputType.streetAddress,
                  textInputAction: TextInputAction.done,
                  // onChangeText: sellerOnBoardingBloc.onTextChange,
                  regExp: AppConstants.onlyStringWithSpace,
                  fieldTextCapitalization: TextCapitalization.sentences,
                  maxCharacter: 50,
                ),
              ),
              horizontalSizedBox(10),
              InkWell(
                onTap: () {
                  rateReviewBloc.addReview(subOrder: subOrder);
                },
                child: Container(
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      color: AppColors.brandBlack),
                  padding:
                      const EdgeInsets.symmetric(horizontal: 30, vertical: 10),
                  child: Text(
                    AppStrings.add,
                    style:
                        AppTextStyle.button2Bold(textColor: AppColors.appWhite),
                  ),
                ),
              )
            ],
          ),
        ],
      ),
    );
  }

//endregion

  //region Review images
  Widget reviewImages({required SubOrder subOrder}) {
    return subOrder.reviewImages.isNotEmpty
        ? Container(
            margin: const EdgeInsets.only(top: 10),
            width: double.infinity,
            height: 100,
            child: ListView.builder(
                itemCount: subOrder.reviewImages.length,
                scrollDirection: Axis.horizontal,
                itemBuilder: (context, index) {
                  return Stack(
                    children: [
                      Container(
                        margin: const EdgeInsets.only(right: 10),
                        width: 100,
                        height: double.infinity,
                        // child: Text(subOrder.reviewImages[index].path),
                        child: Image.file(
                          fit: BoxFit.fill,
                          subOrder.reviewImages[index],
                          cacheHeight: 100,
                          cacheWidth: 100,
                        ),
                      ),
                      Positioned(
                          top: 0,
                          right: 10,
                          child: SizedBox(
                            height: 30,
                            width: 30,
                            child: CupertinoButton(
                              padding: EdgeInsets.zero,
                              onPressed: () {
                                rateReviewBloc.removeReviewImage(
                                    subOrder: subOrder, imageIndex: index);
                              },
                              child: Icon(
                                Icons.close,
                                color: AppColors.appBlack,
                              ),
                            ),
                          ))
                    ],
                  );
                }),
          )
        : const SizedBox();
  }

  //endregion

  //region Add your review
  Widget addYourReview() {
    return InkWell(
      onTap: () {
        rateReviewBloc.onTapAddReview();
      },
      child: Container(
          alignment: Alignment.center,
          margin: const EdgeInsets.symmetric(horizontal: 30),
          padding: const EdgeInsets.symmetric(vertical: 15),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.all(Radius.circular(80)),
              color: AppColors.brandBlack),
          child: appText(
            AppStrings.addYourReview,
            color: AppColors.appWhite,
            fontWeight: FontWeight.w700,
            fontFamily: AppConstants.rRegular,
            fontSize: 15,
          )),
    );
  }

  //endregion

//region Feed back to seller
  Widget feedBack() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        children: [
          Container(
              alignment: Alignment.centerLeft,
              child: appText("Feedback to seller",
                  color: AppColors.appBlack,
                  fontWeight: FontWeight.w600,
                  fontFamily: AppConstants.rRegular,
                  fontSize: 14,
                  opacity: 0.7)),
          verticalSizedBox(10),
          colorFilledTextField(
            context: context,
            textFieldCtrl: TextEditingController(),
            hintText: "write..",
            hintFontSize: 14,
            textFieldMaxLine: 4,
            keyboardType: TextInputType.name,
            textInputAction: TextInputAction.done,
            // onChangeText: sellerOnBoardingBloc.onTextChange,
            regExp: AppConstants.onlyStringWithSpace,
            fieldTextCapitalization: TextCapitalization.words,
            maxCharacter: 50,
          ),
          verticalSizedBox(10),
          Container(
              alignment: Alignment.centerLeft,
              child: appText(
                  "Will you be recommending this store to your friends?",
                  color: AppColors.appBlack,
                  fontWeight: FontWeight.w600,
                  fontFamily: AppConstants.rRegular,
                  fontSize: 14,
                  opacity: 0.7)),
          verticalSizedBox(20),
          Row(
            children: [
              Expanded(
                child: Container(
                    alignment: Alignment.center,
                    padding: const EdgeInsets.symmetric(vertical: 15),
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.all(Radius.circular(80)),
                        color: AppColors.brandBlack),
                    child: appText(
                      "Yes",
                      color: AppColors.appWhite,
                      fontWeight: FontWeight.w700,
                      fontFamily: AppConstants.rRegular,
                      fontSize: 15,
                    )),
              ),
              horizontalSizedBox(10),
              Expanded(
                child: Container(
                    alignment: Alignment.center,
                    padding: const EdgeInsets.symmetric(vertical: 15),
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.all(Radius.circular(80)),
                        color: AppColors.textFieldFill1),
                    child: appText(
                      "Not Today",
                      color: AppColors.writingColor2,
                      fontWeight: FontWeight.w700,
                      fontFamily: AppConstants.rRegular,
                      fontSize: 15,
                    )),
              ),
            ],
          )
        ],
      ),
    );
  }
//endregion
}
