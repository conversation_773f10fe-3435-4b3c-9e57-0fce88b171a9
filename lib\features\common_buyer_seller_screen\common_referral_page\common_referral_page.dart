import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'dart:convert';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/common_buyer_seller_screen/common_referral_page/common_referral_how_swadesic.dart';
import 'package:swadesic/features/common_buyer_seller_screen/common_referral_page/common_referral_page_bloc.dart';
import 'package:swadesic/features/common_buyer_seller_screen/common_referral_page/common_referral_your_referral_makes.dart';
import 'package:swadesic/features/support/support_screen.dart';
import 'package:swadesic/features/buyers/messaging/new_messaging_chat_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/app_web_view/app_web_view.dart';
import 'package:swadesic/model/invite_reward_info/invite_reward_info_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:url_launcher/url_launcher.dart';

// A widget wrapper that maintains the state of its child widget
class _KeepAliveWrapper extends StatefulWidget {
  final Widget child;

  const _KeepAliveWrapper({Key? key, required this.child}) : super(key: key);

  @override
  _KeepAliveWrapperState createState() => _KeepAliveWrapperState();
}

class _KeepAliveWrapperState extends State<_KeepAliveWrapper>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context); // Ensure AutomaticKeepAliveClientMixin works
    return widget.child;
  }
}

class CommonReferralPage extends StatefulWidget {
  const CommonReferralPage({super.key});

  @override
  State<CommonReferralPage> createState() => _CommonReferralPageState();
}

class _CommonReferralPageState extends State<CommonReferralPage>
    with TickerProviderStateMixin {
  //region Bloc
  late CommonReferralPageBloc commonReferralPageBloc;
  //endregion

  //region Tab Controller
  late TabController tabController;
  //endregion

  //region Help Links Data
  Map<String, dynamic> helpLinksData = {};
  //endregion

  //region Init
  @override
  void initState() {
    tabController = TabController(length: 2, vsync: this);
    commonReferralPageBloc = CommonReferralPageBloc(context);
    commonReferralPageBloc.init();
    _loadHelpLinksData();
    super.initState();
  }
  //endregion

  //region Load Help Links Data
  void _loadHelpLinksData() async {
    try {
      final remoteConfig = FirebaseRemoteConfig.instance;
      final helpLinksJson = remoteConfig.getString('app_help_links');
      if (helpLinksJson.isNotEmpty) {
        setState(() {
          helpLinksData = json.decode(helpLinksJson);
        });
      }
    } catch (e) {
      print('Error loading help links: $e');
    }
  }
  //endregion

  //region Dispose
  @override
  void dispose() {
    tabController.dispose();
    super.dispose();
  }
  //endregion

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.appWhite,
      // appBar: appBar(),
      body: Column(
        children: [
          // Tab Bar
          Container(
            padding: const EdgeInsets.only(top: 40),
            color: AppColors.appWhite,
            child: TabBar(
              controller: tabController,
              indicator: UnderlineTabIndicator(
                borderSide: BorderSide(
                  color: AppColors.appBlack,
                  width: 2.0,
                ),
              ),
              labelColor: AppColors.appBlack,
              unselectedLabelColor: AppColors.borderColor1,
              labelStyle: AppTextStyle.access1(textColor: AppColors.appBlack),
              unselectedLabelStyle:
                  AppTextStyle.access1(textColor: AppColors.borderColor1),
              tabs: const [
                Tab(text: "Invite"),
                Tab(text: "Help"),
              ],
            ),
          ),
          // Tab Bar View
          Expanded(
            child: TabBarView(
              controller: tabController,
              children: [
                // Invite Tab (existing content)
                _KeepAliveWrapper(child: inviteTabContent()),
                // Help Tab (new content)
                _KeepAliveWrapper(child: helpTabContent()),
              ],
            ),
          ),
        ],
      ),
    );
  }

  //region Appbar
  AppBar appBar() {
    return AppCommonWidgets.mainAppBar(
      context: context,
      isCustomTitle: false,
      title: "App referral",
      isDefaultMenuVisible: true,
      isMembershipVisible: false,
      isCartVisible: false,
    );
  }

//endregion
  Widget challengeSection() {
    return Container(
      width: MediaQuery.of(context).size.width,
      margin: const EdgeInsets.symmetric(horizontal: 0),
      padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 30),
      decoration: BoxDecoration(
        color: AppColors.appBlack,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Text(
                "⚡",
                style: TextStyle(fontSize: 20),
              ),
              const SizedBox(width: 5),
              Text(
                "The Challenge:",
                style: AppTextStyle.introSlideTitle(textColor: Colors.white)
                    .copyWith(
                        fontSize: 22, fontFamily: AppConstants.leagueSemiBold),
              ),
            ],
          ),
          Text(
            "Bring 3 Buyers. Bring 1 Seller,\nToday. Change the Game.",
            style: AppTextStyle.introSlideTitle(textColor: Colors.white)
                .copyWith(
                    fontSize: 22, fontFamily: AppConstants.leagueSemiBold),
          ),
        ],
      ),
    );
  }

  //region Invite Tab Content
  Widget inviteTabContent() {
    return StreamBuilder<CommonReferralPageState>(
        stream: commonReferralPageBloc.commonReferralPageStateCtrl.stream,
        initialData: CommonReferralPageState.Loading,
        builder: (context, snapshot) {
          //Success
          if (snapshot.data == CommonReferralPageState.Success) {
            return SingleChildScrollView(
              child: Column(
                children: [
                  indiaFlag(),
                  // yourReferralMakesADifference(showTextAndBorder: false),
                  join(),
                  // const SizedBox(height: 30),
                  // challengeSection(),
                  // const SizedBox(height: 30),
                  // rewardSection(),
                  // const SizedBox(height: 30),
                  // howSwadesic(),
                  yourReferralMakesADifference(showTextAndBorder: true),
                  const SizedBox(height: 30),
                  footer(),
                ],
              ),
            );
          }
          //Loading
          if (snapshot.data == CommonReferralPageState.Loading) {
            return AppCommonWidgets.appCircularProgress();
          }
          return const SizedBox();
        });
  }
  //endregion

//region Inda flag
  Widget indiaFlag() {
    return Stack(
      children: [
        Container(
          width: 375,
          height: 235,
          margin: const EdgeInsets.only(left: 0, right: 0, top: 20, bottom: 0),
          child: Image.asset(AppImages.missionPageImage, fit: BoxFit.fill),
        ),
        Positioned.fill(
            child: Container(
          margin: const EdgeInsets.only(bottom: 5),
          alignment: Alignment.bottomCenter,
          // child: Text("Build Atmanirbhar Bharat helping  Swadeshi businesses!",style: AppTextStyle.introSlideTitle(textColor: AppColors.appWhite).copyWith(fontSize: 13,fontFamily: AppConstants.leagueSemiBold),)))
        ))
      ],
    );
  }
//endregion

  //region Join the swadesic
  Widget bulletPoint(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 15, right: 5),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(top: 10, right: 10, left: 10),
            child: Container(
              width: 6,
              height: 6,
              decoration: BoxDecoration(
                color: AppColors.appBlack,
                shape: BoxShape.circle,
              ),
            ),
          ),
          Expanded(
            child: Text(text,
                style: AppTextStyle.settingHeading1(
                    textColor: AppColors.appBlack)),
          ),
        ],
      ),
    );
  }

  Widget join() {
    return Container(
      margin: EdgeInsets.only(top: 20, bottom: 10, right: 15, left: 15),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text("Help us accelerate Swadeshi Mission",
              style: AppTextStyle.exHeading2(textColor: AppColors.appBlack)),
          const SizedBox(height: 20),

          CupertinoButton(
            padding: EdgeInsets.zero,
            onPressed: () {
              if (CommonMethods().isStaticUser()) {
                CommonMethods().goToSignUpFlow();
                return;
              }
              CommonMethods.share(
                  "${AppConstants.appData.isUserView! ? AppStrings.userInviteeMessage : AppStrings.storeInviteeMessage}\n${AppConstants.domainName}?ref=${commonReferralPageBloc.inviteCode}");
            },
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 12),
              // margin: const EdgeInsets.only(left: 10, right: 10, top: 0),
              alignment: Alignment.center,
              decoration: BoxDecoration(
                  color: AppColors.brandBlack,
                  borderRadius: BorderRadius.circular(
                      MediaQuery.of(context).size.width * 0.5)),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // Image.asset(
                  //   AppImages.basketWithStar,
                  //   height: 40,
                  //   width: 40,
                  // ),
                  // const SizedBox(width: 10),
                  Text(
                    "Invite with your code",
                    style: AppTextStyle.access1(textColor: AppColors.appWhite),
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 20),

          Text(
              "Bharat can only grow strong if money stays within. We lose ₹2.2 lakh crore every year through foreign tech, companies, and supply chains — that's 350 Chandrayaan-3 missions gone.",
              style:
                  AppTextStyle.settingHeading1(textColor: AppColors.appBlack)),
          const SizedBox(height: 20),
          Text(
              "We can’t negotiate in critical sectors like defense or energy. But in everything else, we can choose Swadeshi.",
              style:
                  AppTextStyle.settingHeading1(textColor: AppColors.appBlack)),
          const SizedBox(height: 20),
          Text(
              "Swadesic is a tech partner for Indian small businesses — giving them a free store to run business, build community, and offer a professional shopping experience while simplifying their operations.",
              style:
                  AppTextStyle.settingHeading1(textColor: AppColors.appBlack)),
          const SizedBox(height: 20),
          Text(
              "No percentage based commissions.Stores are owned via phone number — just like a digital asset. Perfect for businesses running on WhatsApp or Instagram.",
              style:
                  AppTextStyle.settingHeading1(textColor: AppColors.appBlack)),
          const SizedBox(height: 20),
          Text(
              "For buyers, it's a safe, direct way to shop from brands they love — while discovering local creators and like-minded communities.",
              style:
                  AppTextStyle.settingHeading1(textColor: AppColors.appBlack)),
          const SizedBox(height: 20),

          // bulletPoint(
          //   "We can’t negotiate critical goods like Defense, Energy and Technology sectors."
          // ),
          // bulletPoint(
          //   "At least we can make a strong Swadeshi Economy in other sectors. Swadesic is an initiative to support Indian business ecosystem by being a technology partner & accelerator."
          // ),
          // bulletPoint(
          //   "For someone who runs a business, Swadesic provides a free store to run their operations. Unlike in other platforms, swadesic store is a digital asset that gets registered to user’s phone number."
          // ),
          // bulletPoint(
          //   "With Swadesic stores, businesses can build business & community at one place. Offer a professional shopping & an engaging brand experience to their customers while simplifying business operations."
          // ),
          // bulletPoint(
          //   "For consumers, Swadesic opens up a safe place to shop directly from brands they love on social media and localities. Consumers can know more about the brands, people behind it and others who support same brands and interests."
          // ),
          // const SizedBox(height:20)
        ],
      ),
    );
  }
  //endregion

//region Your referral makes a difference
  Widget yourReferralMakesADifference({bool showTextAndBorder = false}) {
    return CommonReferralYourReferralMakes(
        commonReferralPageBloc: commonReferralPageBloc,
        showTextAndBorder: showTextAndBorder);
  }
//endregion

//region How swadesic
  Widget howSwadesic() {
    return CommonReferralHowSwadesic(
        commonReferralPageBloc: commonReferralPageBloc);
  }
//endregion

  Widget rewardSection() {
    return Container(
      margin: EdgeInsets.only(top: 20, bottom: 10, right: 20, left: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "Because if 10 Crore Indians brought just one more, we'd flip the entire economy in months.",
            style: AppTextStyle.settingHeading1(textColor: AppColors.appBlack),
          ),
          SizedBox(height: 20),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: Text(
                  "💰 Your Reward? A stronger Bharat. A self-reliant economy. Oh, and exclusive perks just for being part of the revolution.",
                  style: AppTextStyle.settingHeading1(
                      textColor: AppColors.appBlack),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

//region Footer
  Widget footer() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 30),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
              "Be part of India's growth story with Swadesic. Be part of the Swadeshi revolution.",
              style:
                  AppTextStyle.settingHeading1(textColor: AppColors.appBlack)),
          const SizedBox(height: 30),
          Text(
              '"Bharat is calling for her noblest sons and daughters. Will you answer the call?"',
              style: AppTextStyle.settingHeading1(textColor: AppColors.appBlack)
                  .copyWith(fontStyle: FontStyle.italic)),
          const SizedBox(height: 10),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Text("— Inspired by Netaji Subhas Chandra Bose",
                  style: AppTextStyle.smallText(textColor: AppColors.appBlack)),
            ],
          ),
        ],
      ),
    );
  }
//endregion

  //region Help Tab Content
  Widget helpTabContent() {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Support Section
            _buildHelpSection(
              title: "Create a Support Ticket",
              subtitle:
                  "Get help with technical issues, feedback, and bug reports for payments, orders, or account issues etc.",
              iconWidget: SvgPicture.asset(AppImages.supportStoreNoFill,
                  height: 30, width: 30, color: AppColors.appBlack),
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const SupportScreen(isReport: false),
                  ),
                );
              },
            ),

            const SizedBox(height: 20),

            // DM Section
            _buildHelpSection(
              title: "DM @swadesic_support",
              subtitle:
                  "Need help, quick doubts, or just want to say hi? Message us inside the app for quick help, tips, or feedback.",
              iconWidget: SvgPicture.asset(
                AppImages.messageInAppBar,
                height: 30,
                width: 30,
                color: AppColors.appBlack,
              ),
              onTap: () {
                if (CommonMethods().isStaticUser()) {
                  CommonMethods().goToSignUpFlow();
                  return;
                }
                final messagingUserId =
                    helpLinksData['support_account_messaging_user_id'] ?? '';
                if (messagingUserId.isNotEmpty) {
                  NewMessagingChatScreen.navigateToChat(context,
                      connectingId: messagingUserId,
                      chatName: 'Swadesic Support',
                      chatIcon: '',
                      entityType: 'USER',
                      chatOwnerReference:
                          helpLinksData['support_account_reference']);
                }
              },
            ),

            const SizedBox(height: 20),

            // WhatsApp Section
            _buildHelpSection(
              title: "Prefer WhatsApp?",
              subtitle: "Tap to open WhatsApp and send us a message.",
              // icon: Icons.chat,
              iconWidget: Image.asset(
                AppImages.whatsappSuperLinkIcon,
                height: 30,
                width: 30,
                color: AppColors.appBlack,
              ),
              onTap: () {
                CommonMethods.messageOnWhatsApp(
                    phoneNumber:
                        helpLinksData['whatsapp_number'] ?? '+************',
                    message: "Hi, I need help with Swadesic");
              },
              // onTap: () {
              //   final whatsappNumber =
              //       helpLinksData['whatsapp_number']?.isNotEmpty ?? true
              //           ? helpLinksData['whatsapp_number']
              //           : '+************';
              //   if (whatsappNumber.isNotEmpty) {
              //     CommonMethods.messageOnWhatsApp(
              //       phoneNumber: whatsappNumber,
              //       message: "Hi, I need help with Swadesic app",
              //     );
              //   }
              // },
            ),

            const SizedBox(height: 30),

            // Learn More Section
            Text(
              "Learn more about Swadesic",
              style: AppTextStyle.exHeading2(textColor: AppColors.appBlack),
            ),

            const SizedBox(height: 20),

            // Website Link
            _buildLinkTile(
              title: "Visit swadesic.sociallyx.com",
              icon: Icons.language,
              onTap: () {
                final websiteUrl = helpLinksData['website_url'] ??
                    'https://swadesic.sociallyx.com';
                _openWebView(websiteUrl, false);
              },
            ),

            const SizedBox(height: 15),

            // Blog Link
            _buildLinkTile(
              title: "Read our Blog",
              subtitle:
                  "Learn more about Swadesic mission, Tech learnings, How-to guides, Selling tips and more.",
              icon: Icons.article,
              onTap: () {
                final blogUrl = helpLinksData['blog_url'] ??
                    'https://swadesic.sociallyx.com/blog';
                if (blogUrl.isNotEmpty) {
                  _openWebView(blogUrl, false);
                }
              },
            ),

            const SizedBox(height: 15),

            // Android App Link
            _buildLinkTile(
              title: "Download Swadesic App",
              subtitle: "For Android users",
              // iconWidget: SvgPicture.asset(AppImages.appIcon),
              icon: Icons.get_app,
              onTap: () async {
                final webAppUrl = helpLinksData['android_app_url'] ??
                    'https://play.google.com/store/apps/details?id=com.sociallyx.swadesic&hl=en_SG';
                if (webAppUrl.isNotEmpty) {
                  final uri = Uri.parse(webAppUrl);
                  if (await canLaunchUrl(uri)) {
                    await launchUrl(uri);
                  } else {
                    throw 'Could not launch $webAppUrl';
                  }
                }
              },
            ),

            const SizedBox(height: 15),

            // Web App Link
            _buildLinkTile(
              title: "Visit Swadesic Web App",
              subtitle: "For iOs and Web users",
              icon: Icons.web,
              onTap: () {
                final webAppUrl =
                    helpLinksData['web_app_url'] ?? 'https://swadesic.com';
                if (webAppUrl.isNotEmpty) {
                  _openWebView(webAppUrl, false);
                }
              },
            ),

            const SizedBox(height: 30),

            // Social Media Section
            Text(
              "Follow us on Social media",
              style: AppTextStyle.exHeading2(textColor: AppColors.appBlack),
            ),

            const SizedBox(height: 20),

            // Social Media Icons Row
            _buildSocialMediaRow(),

            const SizedBox(height: 30),
          ],
        ),
      ),
    );
  }
  //endregion

  //region Help Section Builder
  Widget _buildHelpSection({
    required String title,
    required String subtitle,
    IconData? icon,
    Widget? iconWidget,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border.all(color: AppColors.borderColor1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              // decoration: BoxDecoration(
              //   color: AppColors.appBlack.withOpacity(0.1),
              //   borderRadius: BorderRadius.circular(8),
              // ),
              child: iconWidget ??
                  Icon(
                    icon,
                    size: 24,
                    color: AppColors.appBlack,
                  ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: AppTextStyle.access1(textColor: AppColors.appBlack),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: AppTextStyle.smallText(
                        textColor: AppColors.writingColor2),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: AppColors.borderColor1,
            ),
          ],
        ),
      ),
    );
  }
  //endregion

  //region Link Tile Builder
  Widget _buildLinkTile({
    required String title,
    String? subtitle,
    IconData? icon,
    Widget? iconWidget,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 8),
        child: Row(
          children: [
            iconWidget ??
                Icon(
                  icon,
                  size: 20,
                  color: AppColors.appBlack,
                ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: AppTextStyle.access1(textColor: AppColors.appBlack),
                  ),
                  if (subtitle != null) ...[
                    const SizedBox(height: 2),
                    Text(
                      subtitle,
                      style: AppTextStyle.smallText(
                          textColor: AppColors.writingColor2),
                    ),
                  ],
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              size: 14,
              color: AppColors.borderColor1,
            ),
          ],
        ),
      ),
    );
  }
  //endregion

  //region Social Media Row
  Widget _buildSocialMediaRow() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        _buildSocialIcon(
          imagePath: AppImages.instagramSuperLinkIcon,
          onTap: () {
            final instagramUrl = helpLinksData['instagram_url'] ??
                'https://instagram.com/swadesic';
            if (instagramUrl.isNotEmpty) {
              CommonMethods.launchSocialMediaUrl(url: instagramUrl);
            }
          },
        ),
        _buildSocialIcon(
          imagePath: AppImages.xSuperLinkIcon,
          onTap: () {
            final twitterUrl =
                helpLinksData['twitter_url'] ?? 'https://x.com/SwadesicApp';
            if (twitterUrl.isNotEmpty) {
              CommonMethods.launchSocialMediaUrl(url: twitterUrl);
            }
          },
        ),
        _buildSocialIcon(
          imagePath: AppImages.linkedinSuperLinkIcon,
          onTap: () async {
            final webAppUrl = helpLinksData['linkedin_app_url'] ??
                'https://www.linkedin.com/company/socially-x/';
            if (webAppUrl.isNotEmpty) {
              if (await canLaunchUrl(Uri.parse(webAppUrl))) {
                await launchUrl(Uri.parse(webAppUrl));
              } else {
                throw 'Could not launch $webAppUrl';
              }
            }
          },
        ),
        _buildSocialIcon(
          imagePath: AppImages.maintenanceThreadsAppIcon,
          onTap: () {
            final facebookUrl = helpLinksData['threads_url'] ??
                'https://www.threads.com/@swadesic';
            if (facebookUrl.isNotEmpty) {
              CommonMethods.launchSocialMediaUrl(url: facebookUrl);
            }
          },
        ),
        _buildSocialIcon(
          imagePath: AppImages.youtubeLinkIcon,
          onTap: () {
            final youtubeUrl = helpLinksData['youtube_url'] ??
                'https://www.youtube.com/@swadesic';
            if (youtubeUrl.isNotEmpty) {
              CommonMethods.launchSocialMediaUrl(url: youtubeUrl);
            }
          },
        ),
      ],
    );
  }
  //endregion

  //region Social Icon Builder
  Widget _buildSocialIcon({
    required String imagePath,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: AppColors.borderColor1.withOpacity(0.3)),
        ),
        child: Image.asset(
          imagePath,
          height: 32,
          width: 32,
          color: AppColors.appBlack,
        ),
      ),
    );
  }
  //endregion

  //region Open Web View
  void _openWebView(String url, bool showAppBar) {
    if (url.isNotEmpty) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => AppWebView(url: url, showAppBar: showAppBar),
        ),
      );
    }
  }
  //endregion
}
