import 'package:flutter/material.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

class AddProductCommonWidgets{
  //region Title
  static Widget title({required String title}){
    return appText(title,fontWeight:FontWeight.w600,fontSize: 14,fontFamily:AppConstants.rRegular,
    maxLine: 5,
      color: AppColors.writingColor2,

    );
    // return Text( AppStrings.pricing,style: TextStyle(
    //     fontWeight: FontWeight.w600,
    //     fontSize: 14,
    //     color: AppColors.appBlack5,
    //     fontFamily: "LatoSemibold"
    // ),);
  }
  //endregion

//region Delivery return button
 static Widget subPageButton({required String buttonName,required onPress,required BuildContext context,bool isDoneVisible = false}){
    return Row(
      children: [
        InkWell(
          onTap: (){
            onPress();
          },
          child: Container(
            width: CommonMethods.textWidth(context: context, textStyle: AppTextStyle.access0(textColor: AppColors.appBlack),text: 'Set Fulfillment settings') + 50,
              padding: const EdgeInsets.symmetric(vertical:10,horizontal: 20),
              decoration:  BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                color: AppColors.textFieldFill1,
              ),
              child:Text(buttonName,style: AppTextStyle.access0(textColor: AppColors.appBlack),)
          ),
        ),
        Visibility(
          visible:isDoneVisible ,
          child: Container(margin: const EdgeInsets.only(left: 10),
          height: 24,
          width: 24,
          child: Image.asset(AppImages.checkListDone,height: 24,width: 24,),
          ),
        )
      ],
    );
  }
//endregion


}