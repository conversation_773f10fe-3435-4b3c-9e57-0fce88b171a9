import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:swadesic/features/seller/unified_product_management/unified_product_form_bloc.dart';
import 'package:swadesic/features/seller/unified_product_management/components/product_image_manager.dart';
import 'package:swadesic/features/seller/unified_product_management/components/section_navigation_list.dart';
import 'package:swadesic/features/seller/unified_product_management/models/product_form_section.dart';
import 'package:swadesic/features/seller/unified_product_management/sections/basic_details_section.dart';
import 'package:swadesic/features/seller/unified_product_management/sections/inventory_section.dart';
import 'package:swadesic/features/seller/unified_product_management/sections/swadeshi_labels_section.dart';
import 'package:swadesic/model/unified_product_form_data/unified_product_form_data.dart';
import 'package:swadesic/model/unified_product_image_state/unified_product_image_state.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

// Import existing screens for section navigation
import 'package:swadesic/features/seller/labels/labels_screen.dart';
import 'package:swadesic/features/seller/inventory_options/inventory_options_screen.dart';
import 'package:swadesic/features/seller/seller_store/seller_store_delivery_setting/seller_store_delivey_setting_screen.dart';
import 'package:swadesic/features/seller/seller_store/seller_return_store_warranty/seller_return_store_warranty_screen.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart';
import 'package:swadesic/model/seller_delivery_setting_response/seller_delivery_setting_response.dart';
import 'package:swadesic/model/seller_return_warranty_response/seller_return_warranty_response.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/features/seller/add_edit_product_fields/add_edit_product_fields_bloc.dart';

/// Main unified product management screen
class UnifiedProductManagementScreen extends StatefulWidget {
  final int storeId;
  final String storeReference;
  final String? productReference; // null for add mode, provided for edit mode

  const UnifiedProductManagementScreen({
    Key? key,
    required this.storeId,
    required this.storeReference,
    this.productReference,
  }) : super(key: key);

  @override
  State<UnifiedProductManagementScreen> createState() =>
      _UnifiedProductManagementScreenState();
}

class _UnifiedProductManagementScreenState
    extends State<UnifiedProductManagementScreen> {
  UnifiedProductFormBloc? _formBloc;
  bool _isInitialized = false;

  // Form data storage
  Map<String, dynamic> _formData = {
    'basic_details': {
      'product_name': '',
      'brand_name': '',
      'category': '',
      'description': '',
    },
    'inventory': {
      'options': <String, List<String>>{},
      'variants': <Map<String, dynamic>>[],
      'hasMultipleOptions': false,
    },
    'swadeshi_labels': null, // Will store Product object
    'delivery_settings': null, // Will store SellerDeliveryStoreResponse
    'return_settings': null, // Will store SellerReturnWarrantyResponse
    'product_promotions': {
      'enabled': false,
      'amount': 0,
    },
    'visibility': {
      'slug': '',
      'code': '',
      'hashtags': '',
    },
    'more_details': {
      'promotion_link': '',
    },
  };

  // Image data storage - use existing AppConstants for compatibility
  bool get _hasImages {
    if (kIsWeb) {
      return AppConstants.webProductImages.isNotEmpty;
    } else {
      return AppConstants.multipleSelectedImage.isNotEmpty;
    }
  }

  int get _imageCount {
    if (kIsWeb) {
      return AppConstants.webProductImages.length;
    } else {
      return AppConstants.multipleSelectedImage.length;
    }
  }

  @override
  void initState() {
    super.initState();
    try {
      debugPrint('UnifiedProductManagementScreen: initState started');
      debugPrint(
          'UnifiedProductManagementScreen: storeId=${widget.storeId}, storeReference=${widget.storeReference}, productReference=${widget.productReference}');

      // For testing navigation, skip bloc initialization but mark as initialized
      if (kDebugMode) {
        debugPrint(
            'UnifiedProductManagementScreen: Test mode - skipping bloc initialization');
        setState(() {
          _isInitialized = true;
        });
        return;
      }

      // Initialize the form bloc
      _formBloc = UnifiedProductFormBloc(
        context: context,
        storeId: widget.storeId,
        storeReference: widget.storeReference,
        productReference: widget.productReference,
      );
      debugPrint(
          'UnifiedProductManagementScreen: FormBloc initialized successfully');

      // Initialize the form asynchronously
      _initializeForm();
    } catch (e, stackTrace) {
      debugPrint('UnifiedProductManagementScreen: initState failed: $e');
      debugPrint('Stack trace: $stackTrace');
    }
  }

  Future<void> _initializeForm() async {
    if (_formBloc == null) return;

    try {
      debugPrint(
          'UnifiedProductManagementScreen: Starting form initialization...');
      await _formBloc!.initialize();
      debugPrint(
          'UnifiedProductManagementScreen: Form initialization completed');
      if (mounted) {
        setState(() {
          _isInitialized = true;
        });
        debugPrint(
            'UnifiedProductManagementScreen: State updated to initialized');
      }
    } catch (e, stackTrace) {
      debugPrint('UnifiedProductManagementScreen: Initialization failed: $e');
      debugPrint('Stack trace: $stackTrace');
      if (mounted) {
        CommonMethods.toastMessage(
          'Failed to initialize form: ${e.toString()}',
          context,
        );
      }
    }
  }

  @override
  void dispose() {
    _formBloc?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: _onWillPop,
      child: GestureDetector(
        onTap: () => CommonMethods.closeKeyboard(context),
        child: Scaffold(
          backgroundColor: AppColors.appWhite,
          appBar: _buildAppBar(),
          body: _isInitialized ? _buildBody() : _buildLoadingState(),
          bottomNavigationBar: _isInitialized ? _buildBottomBar() : null,
        ),
      ),
    );
  }

  AppBar _buildAppBar() {
    return AppCommonWidgets.mainAppBar(
      context: context,
      isCustomTitle: false,
      title: _formBloc?.isEditMode == true ? 'Edit Product' : 'Add Product',
      isDefaultMenuVisible: false,
      isMembershipVisible: false,
      isCartVisible: false,
      isTextButtonVisible: true,
      textButtonWidget: _formBloc != null
          ? StreamBuilder<UnifiedProductFormData>(
              stream: _formBloc!.formDataStream,
              builder: (context, snapshot) {
                final hasChanges =
                    snapshot.hasData && _formBloc!.hasUnsavedChanges;
                return AppCommonWidgets.appBarTextButtonText(
                  text: hasChanges ? 'Save Draft' : 'Reset',
                );
              },
            )
          : AppCommonWidgets.appBarTextButtonText(text: 'next'),
      onTapTextButton: _formBloc != null ? _onTapAppBarAction : _onTapMockNext,
    );
  }

  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(color: AppColors.brandBlack),
          verticalSizedBox(16),
          Text(
            _formBloc?.isEditMode == true
                ? 'Loading product details...'
                : 'Initializing form...',
            style:
                AppTextStyle.contentText0(textColor: AppColors.writingColor2),
          ),
        ],
      ),
    );
  }

  Widget _buildBody() {
    // For testing, show the form UI with mock data when bloc is not initialized
    if (_formBloc == null) {
      return _buildMockFormContent();
    }

    return StreamBuilder<UnifiedProductFormState>(
      stream: _formBloc!.formStateStream,
      builder: (context, stateSnapshot) {
        if (stateSnapshot.data == UnifiedProductFormState.loading) {
          return _buildLoadingState();
        }

        if (stateSnapshot.data == UnifiedProductFormState.error) {
          return _buildErrorState();
        }

        return StreamBuilder<UnifiedProductFormData>(
          stream: _formBloc!.formDataStream,
          builder: (context, dataSnapshot) {
            if (!dataSnapshot.hasData) {
              return _buildLoadingState();
            }

            return SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildProgressIndicator(dataSnapshot.data!),
                  verticalSizedBox(20),
                  _buildImageSection(dataSnapshot.data!),
                  verticalSizedBox(24),
                  _buildSectionNavigation(dataSnapshot.data!),
                  verticalSizedBox(100), // Space for bottom bar
                ],
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildErrorState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: AppColors.writingColor2,
          ),
          verticalSizedBox(16),
          Text(
            'Failed to load form',
            style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
          ),
          verticalSizedBox(8),
          Text(
            'Please try again',
            style:
                AppTextStyle.contentText0(textColor: AppColors.writingColor2),
          ),
          verticalSizedBox(16),
          ElevatedButton(
            onPressed: _initializeForm,
            child: Text('Retry'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.brandBlack,
              foregroundColor: AppColors.appWhite,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMockFormContent() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildMockImageSection(),
          verticalSizedBox(24),
          _buildMockSectionNavigation(),
          verticalSizedBox(100), // Space for bottom bar
        ],
      ),
    );
  }

  Widget _buildMockImageSection() {
    return GestureDetector(
      onTap: _handleImageUpload,
      child: Container(
        width: double.infinity,
        height: !_hasImages ? 200 : null,
        decoration: BoxDecoration(
          color: AppColors.textFieldFill1,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: AppColors.borderColor1,
            width: 1,
          ),
        ),
        child: !_hasImages
            ? Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.add_photo_alternate_outlined,
                    size: 48,
                    color: AppColors.writingColor2,
                  ),
                  verticalSizedBox(8),
                  Text(
                    'Add Product Images',
                    style: AppTextStyle.contentText0(
                        textColor: AppColors.writingColor2),
                  ),
                  verticalSizedBox(4),
                  Text(
                    'Tap to upload photos',
                    style: AppTextStyle.contentText1(
                        textColor: AppColors.writingColor3),
                  ),
                ],
              )
            : _buildImageGrid(),
      ),
    );
  }

  Widget _buildImageGrid() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Product Images ($_imageCount)',
                style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
              ),
              GestureDetector(
                onTap: _handleImageUpload,
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: AppColors.brandBlack,
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Text(
                    'Add More',
                    style: AppTextStyle.contentText1(
                        textColor: AppColors.appWhite),
                  ),
                ),
              ),
            ],
          ),
          verticalSizedBox(12),
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 3,
              crossAxisSpacing: 8,
              mainAxisSpacing: 8,
              childAspectRatio: 1,
            ),
            itemCount: _imageCount,
            itemBuilder: (context, index) {
              return Stack(
                children: [
                  Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      color: AppColors.borderColor1,
                    ),
                    child: _buildImagePreview(index),
                  ),
                  Positioned(
                    top: 4,
                    right: 4,
                    child: GestureDetector(
                      onTap: () => _removeImage(index),
                      child: Container(
                        padding: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          color: Colors.red,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Icon(
                          Icons.close,
                          size: 16,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),
                ],
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildImagePreview(int index) {
    if (kIsWeb) {
      if (index < AppConstants.webProductImages.length) {
        return ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: Image.memory(
            AppConstants.webProductImages[index]['bytes'],
            fit: BoxFit.cover,
            width: double.infinity,
            height: double.infinity,
          ),
        );
      }
    } else {
      if (index < AppConstants.multipleSelectedImage.length) {
        return ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: Image.file(
            File(AppConstants.multipleSelectedImage[index].path),
            fit: BoxFit.cover,
            width: double.infinity,
            height: double.infinity,
          ),
        );
      }
    }

    return Center(
      child: Icon(
        Icons.image,
        size: 40,
        color: AppColors.writingColor2,
      ),
    );
  }

  Widget _buildMockSectionNavigation() {
    final sections = [
      {
        'title': 'Basic details',
        'completed': _isBasicDetailsCompleted(),
      },
      {
        'title': 'Inventory',
        'completed': _isInventoryCompleted(),
      },
      {
        'title': 'Swadeshi labels',
        'completed': _isSwadeshiLabelsCompleted(),
      },
      {
        'title': 'Delivery settings',
        'completed': _isDeliverySettingsCompleted(),
      },
      {
        'title': 'Return settings',
        'completed': _isReturnSettingsCompleted(),
      },
      {
        'title': 'Product promotions',
        'completed': _isProductPromotionsCompleted(),
      },
      {
        'title': 'Visibility',
        'completed': _isVisibilityCompleted(),
      },
      {
        'title': 'More details',
        'completed': _isMoreDetailsCompleted(),
      },
      {'title': 'Tag Stories', 'completed': false, 'subtitle': 'coming soon'},
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Product Information',
          style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
        ),
        verticalSizedBox(16),
        ...sections
            .map((section) => _buildMockSectionItem(
                  title: section['title'] as String,
                  completed: section['completed'] as bool,
                  subtitle: section['subtitle'] as String?,
                ))
            .toList(),
      ],
    );
  }

  Widget _buildMockSectionItem({
    required String title,
    required bool completed,
    String? subtitle,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
          side: BorderSide(color: AppColors.borderColor1),
        ),
        leading: Icon(
          completed ? Icons.check_circle : Icons.radio_button_unchecked,
          color: completed ? AppColors.brandGreen : AppColors.writingColor2,
        ),
        title: Text(
          title,
          style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
        ),
        subtitle: subtitle != null
            ? Text(
                subtitle,
                style: AppTextStyle.contentText1(
                    textColor: AppColors.writingColor3),
              )
            : null,
        trailing: Icon(
          Icons.chevron_right,
          color: AppColors.writingColor2,
        ),
        onTap: () => _navigateToSection(title),
      ),
    );
  }

  Widget _buildMockBottomBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.appWhite,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Row(
          children: [
            Expanded(
              flex: 2,
              child: ElevatedButton(
                onPressed: _showFormDataDebug,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.borderColor1,
                  foregroundColor: AppColors.appBlack,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: Text(
                  'Debug Data',
                  style:
                      AppTextStyle.button2Bold(textColor: AppColors.appBlack),
                ),
              ),
            ),
            horizontalSizedBox(12),
            Expanded(
              flex: 3,
              child: ElevatedButton(
                onPressed: () {
                  _showFormDataSummary();
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.brandBlack,
                  foregroundColor: AppColors.appWhite,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: Text(
                  'Preview & Publish',
                  style:
                      AppTextStyle.button2Bold(textColor: AppColors.appWhite),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressIndicator(UnifiedProductFormData formData) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.textFieldFill1,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                'Form Progress',
                style:
                    AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
              ),
              const Spacer(),
              Text(
                '${formData.completedSectionsCount}/${formData.totalSectionsCount}',
                style: AppTextStyle.contentText0(
                    textColor: AppColors.writingColor2),
              ),
            ],
          ),
          verticalSizedBox(8),
          LinearProgressIndicator(
            value: formData.completionPercentage,
            backgroundColor: AppColors.borderColor1,
            valueColor: AlwaysStoppedAnimation<Color>(AppColors.brandBlack),
          ),
          verticalSizedBox(8),
          StreamBuilder<FormValidationResult>(
            stream: _formBloc!.validationStream,
            builder: (context, validationSnapshot) {
              if (validationSnapshot.hasData &&
                  validationSnapshot.data!.hasErrors) {
                return Row(
                  children: [
                    Icon(
                      Icons.warning_amber_outlined,
                      size: 16,
                      color: Colors.orange,
                    ),
                    horizontalSizedBox(4),
                    Text(
                      '${validationSnapshot.data!.totalErrorCount} issues to resolve',
                      style: AppTextStyle.smallText(textColor: Colors.orange),
                    ),
                  ],
                );
              }
              return Row(
                children: [
                  Icon(
                    Icons.check_circle_outline,
                    size: 16,
                    color: AppColors.brandBlack,
                  ),
                  horizontalSizedBox(4),
                  Text(
                    formData.hasRequiredSections
                        ? 'Ready for preview'
                        : 'Complete required sections',
                    style: AppTextStyle.smallText(
                      textColor: formData.hasRequiredSections
                          ? AppColors.brandBlack
                          : AppColors.writingColor2,
                    ),
                  ),
                ],
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildImageSection(UnifiedProductFormData formData) {
    if (_formBloc == null) return const SizedBox.shrink();

    return StreamBuilder<UnifiedProductImageState>(
      stream: _formBloc!.imageStateStream,
      builder: (context, imageSnapshot) {
        final imageState = imageSnapshot.data ?? formData.imageState;

        return ProductImageManager(
          imageState: imageState,
          onImageStateChanged: _formBloc!.updateImageState,
          maxImages: 10,
          isEditMode: _formBloc!.isEditMode,
        );
      },
    );
  }

  Widget _buildSectionNavigation(UnifiedProductFormData formData) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Product Information',
          style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
        ),
        verticalSizedBox(16),
        StreamBuilder<Map<String, bool>>(
          stream: _formBloc!.sectionCompletionStream,
          builder: (context, completionSnapshot) {
            return SectionNavigationList(
              formBloc: _formBloc!,
              sectionCompletion:
                  completionSnapshot.data ?? formData.sectionCompletion,
              onSectionTap: _onSectionTap,
            );
          },
        ),
      ],
    );
  }

  Widget _buildBottomBar() {
    if (_formBloc == null) return _buildMockBottomBar();

    return StreamBuilder<FormValidationResult>(
      stream: _formBloc!.validationStream,
      builder: (context, validationSnapshot) {
        return Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppColors.appWhite,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 8,
                offset: const Offset(0, -2),
              ),
            ],
          ),
          child: SafeArea(
            child: Row(
              children: [
                Expanded(
                  child: StreamBuilder<UnifiedProductFormData>(
                    stream: _formBloc!.formDataStream,
                    builder: (context, dataSnapshot) {
                      final canProceed = dataSnapshot.hasData &&
                          dataSnapshot.data!.hasRequiredSections &&
                          (validationSnapshot.data?.isValid ?? false);

                      return ElevatedButton(
                        onPressed: canProceed ? _onTapPreview : null,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.brandBlack,
                          foregroundColor: AppColors.appWhite,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: Text(
                          'Preview & Publish',
                          style: AppTextStyle.button2Bold(
                              textColor: AppColors.appWhite),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // Section completion check methods
  bool _isBasicDetailsCompleted() {
    final basicDetails = _formData['basic_details'];
    return basicDetails['product_name']?.isNotEmpty == true &&
        basicDetails['brand_name']?.isNotEmpty == true &&
        basicDetails['category']?.isNotEmpty == true;
  }

  bool _isProductPromotionsCompleted() {
    final promotions = _formData['product_promotions'];
    return promotions['enabled'] == true && promotions['amount'] > 0;
  }

  bool _isVisibilityCompleted() {
    final visibility = _formData['visibility'];
    return visibility['slug']?.isNotEmpty == true ||
        visibility['code']?.isNotEmpty == true ||
        visibility['hashtags']?.isNotEmpty == true;
  }

  bool _isMoreDetailsCompleted() {
    final moreDetails = _formData['more_details'];
    return moreDetails['promotion_link']?.isNotEmpty == true;
  }

  bool _isInventoryCompleted() {
    final inventory = _formData['inventory'] as Map<String, dynamic>;
    return inventory['variants']?.isNotEmpty == true ||
        inventory['options']?.isNotEmpty == true;
  }

  bool _isSwadeshiLabelsCompleted() {
    final labels = _formData['swadeshi_labels'] as Product?;
    return labels != null &&
        (labels.swadeshiBrand?.isNotEmpty == true ||
            labels.swadeshiMade?.isNotEmpty == true ||
            labels.swadeshiOwned?.isNotEmpty == true);
  }

  bool _isDeliverySettingsCompleted() {
    return _formData['delivery_settings'] != null;
  }

  bool _isReturnSettingsCompleted() {
    return _formData['return_settings'] != null;
  }

  void _navigateToSection(String sectionTitle) async {
    debugPrint(
        'UnifiedProductManagementScreen: Navigating to section: $sectionTitle');
    debugPrint('UnifiedProductManagementScreen: Store ID: ${widget.storeId}');
    debugPrint(
        'UnifiedProductManagementScreen: Store Reference: ${widget.storeReference}');
    debugPrint(
        'UnifiedProductManagementScreen: Product Reference: ${widget.productReference}');

    Widget? sectionScreen;

    switch (sectionTitle) {
      case 'Basic details':
        sectionScreen = _createBasicDetailsScreen();
        break;
      case 'Inventory':
        sectionScreen = _createInventoryScreen();
        break;
      case 'Swadeshi labels':
        sectionScreen = _createSwadeshiLabelsScreen();
        break;
      case 'Delivery settings':
        sectionScreen = _createDeliverySettingsScreen();
        break;
      case 'Return settings':
        sectionScreen = _createReturnSettingsScreen();
        break;
      case 'Product promotions':
        sectionScreen = _createProductPromotionsScreen();
        break;
      case 'Visibility':
        sectionScreen = _createVisibilityScreen();
        break;
      case 'More details':
        sectionScreen = _createMoreDetailsScreen();
        break;
      case 'Tag Stories':
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Tag Stories - Coming soon'),
            duration: Duration(seconds: 2),
          ),
        );
        return;
      default:
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Section "$sectionTitle" not implemented yet'),
            duration: const Duration(seconds: 2),
          ),
        );
        return;
    }

    if (sectionScreen != null) {
      final result = await Navigator.push(
        context,
        MaterialPageRoute(builder: (context) => sectionScreen!),
      );

      if (result != null) {
        debugPrint(
            'UnifiedProductManagementScreen: Section returned data: $result');
        // Handle the returned data here
        setState(() {
          // Trigger rebuild to show updated data
        });
      }
    }
  }

  void _handleImageUpload() {
    debugPrint('UnifiedProductManagementScreen: Image upload tapped');

    // Simulate image selection
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add Images'),
        content: const Text('Select images to upload'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _addMockImages();
            },
            child: const Text('Add Mock Images'),
          ),
        ],
      ),
    );
  }

  void _addMockImages() {
    setState(() {
      // Add mock images to AppConstants for compatibility
      if (kIsWeb) {
        // Add mock web images
        for (int i = 0; i < 3; i++) {
          AppConstants.webProductImages.add({
            'bytes': Uint8List(0), // Empty bytes for mock
            'name':
                'mock_image_${AppConstants.webProductImages.length + 1}.jpg',
          });
        }
      } else {
        // Add mock mobile images - this would normally be XFile objects
        // For now just add empty entries to demonstrate count
        // In real implementation, this would be handled by image picker
      }
    });

    debugPrint(
        'UnifiedProductManagementScreen: Added mock images, total count: $_imageCount');

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Added mock images (total: $_imageCount)'),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _removeImage(int index) {
    setState(() {
      if (kIsWeb) {
        if (index < AppConstants.webProductImages.length) {
          AppConstants.webProductImages.removeAt(index);
        }
      } else {
        if (index < AppConstants.multipleSelectedImage.length) {
          AppConstants.multipleSelectedImage.removeAt(index);
        }
      }
    });

    debugPrint('UnifiedProductManagementScreen: Removed image at index $index');

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Image removed'),
        duration: Duration(seconds: 1),
      ),
    );
  }

  void _showFormDataDebug() {
    debugPrint('UnifiedProductManagementScreen: Current form data:');
    debugPrint('Images: $_imageCount');
    debugPrint('Form data: $_formData');

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Form Data Debug'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('Images: $_imageCount'),
              const SizedBox(height: 8),
              Text('Basic Details: ${_formData['basic_details']}'),
              const SizedBox(height: 8),
              Text('Promotions: ${_formData['product_promotions']}'),
              const SizedBox(height: 8),
              Text('Visibility: ${_formData['visibility']}'),
              const SizedBox(height: 8),
              Text('More Details: ${_formData['more_details']}'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showFormDataSummary() {
    final completedSections = <String>[];
    if (_isBasicDetailsCompleted()) completedSections.add('Basic Details');
    if (_isInventoryCompleted()) completedSections.add('Inventory');
    if (_isSwadeshiLabelsCompleted()) completedSections.add('Swadeshi Labels');
    if (_isDeliverySettingsCompleted())
      completedSections.add('Delivery Settings');
    if (_isReturnSettingsCompleted()) completedSections.add('Return Settings');
    if (_isProductPromotionsCompleted())
      completedSections.add('Product Promotions');
    if (_isVisibilityCompleted()) completedSections.add('Visibility');
    if (_isMoreDetailsCompleted()) completedSections.add('More Details');

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Product Summary'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('Images: $_imageCount'),
              const SizedBox(height: 16),
              Text('Completed Sections (${completedSections.length}/8):'),
              const SizedBox(height: 8),
              ...completedSections.map((section) => Padding(
                    padding: const EdgeInsets.only(left: 16, bottom: 4),
                    child: Text('✓ $section'),
                  )),
              if (completedSections.isEmpty)
                const Padding(
                  padding: EdgeInsets.only(left: 16),
                  child: Text('No sections completed yet'),
                ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
          if (completedSections.isNotEmpty)
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text(
                        'Mock interface - Product creation not implemented yet'),
                    duration: Duration(seconds: 2),
                  ),
                );
              },
              child: const Text('Create Product'),
            ),
        ],
      ),
    );
  }

  void _onTapMockNext() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content:
            Text('Mock interface - Next functionality not implemented yet'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  // Screen creation methods for each section
  Widget _createBasicDetailsScreen() {
    return _BasicDetailsFormScreen(
      initialData: _formData['basic_details'],
      onSave: (data) {
        setState(() {
          _formData['basic_details'] = data;
        });
        debugPrint(
            'UnifiedProductManagementScreen: Basic details saved: $data');
      },
    );
  }

  Widget _createInventoryScreen() {
    // Create a temporary product for inventory options with existing data
    final inventoryData = _formData['inventory'] as Map<String, dynamic>;
    final basicDetails = _formData['basic_details'] as Map<String, dynamic>;

    Product tempProduct = Product(
      productReference: widget.productReference ?? "new_product",
      productName: basicDetails['product_name'] ?? "Product Name",
      brandName: basicDetails['brand_name'] ?? "Brand Name",
      options: inventoryData['options'] as Map<String, List<String>>?,
      variants: (inventoryData['variants'] as List<dynamic>?)
          ?.cast<Map<String, dynamic>>(),
    );

    return InventoryOptionsScreen(
      storeReference: widget.storeReference,
      product: tempProduct,
    );
  }

  Widget _createSwadeshiLabelsScreen() {
    // Create a temporary product for labels with existing data
    final basicDetails = _formData['basic_details'] as Map<String, dynamic>;
    final existingLabelsData = _formData['swadeshi_labels'] as Product?;

    Product tempProduct = existingLabelsData ??
        Product(
          productReference: widget.productReference ?? "new_product",
          productName: basicDetails['product_name'] ?? "Product Name",
          brandName: basicDetails['brand_name'] ?? "Brand Name",
        );

    return LabelsScreen(
      isFromTrustCenter: false,
      storeReference: widget.storeReference,
      product: tempProduct,
      brand: existingLabelsData?.swadeshiBrand,
      owned: existingLabelsData?.swadeshiOwned,
      made: existingLabelsData?.swadeshiMade,
    );
  }

  Widget _createDeliverySettingsScreen() {
    return SellerStoreDeliverySettingScreen(
      storeRef: widget.storeReference,
      isFromAddProduct: true,
      isFromStore: false,
      isFromEditProduct: widget.productReference != null,
      deliverySettingsDataFromAddProduct:
          null, // This would come from form data
    );
  }

  Widget _createReturnSettingsScreen() {
    return SellerReturnStoreWarrantyScreen(
      storeRef: widget.storeReference,
      isFromAddProduct: true,
      fromEditProductScreen: widget.productReference != null,
      fromStoreScreen: false,
      fromProductScreen: false,
      returnSettingsDataFromAddProduct: null, // This would come from form data
    );
  }

  Widget _createProductPromotionsScreen() {
    return _ProductPromotionsFormScreen(
      initialData: _formData['product_promotions'],
      onSave: (data) {
        setState(() {
          _formData['product_promotions'] = data;
        });
        debugPrint(
            'UnifiedProductManagementScreen: Product promotions saved: $data');
      },
    );
  }

  Widget _createVisibilityScreen() {
    return _VisibilityFormScreen(
      initialData: _formData['visibility'],
      onSave: (data) {
        setState(() {
          _formData['visibility'] = data;
        });
        debugPrint('UnifiedProductManagementScreen: Visibility saved: $data');
      },
    );
  }

  Widget _createMoreDetailsScreen() {
    return _MoreDetailsFormScreen(
      initialData: _formData['more_details'],
      onSave: (data) {
        setState(() {
          _formData['more_details'] = data;
        });
        debugPrint('UnifiedProductManagementScreen: More details saved: $data');
      },
    );
  }

  void _onSectionTap(ProductFormSectionType sectionType) async {
    if (_formBloc == null) return;

    final currentData = _formBloc!.getSectionData(sectionType);
    Widget? sectionScreen;

    switch (sectionType) {
      case ProductFormSectionType.basicDetails:
        sectionScreen = BasicDetailsSection(
          formBloc: _formBloc!,
          initialData: currentData,
        );
        break;
      case ProductFormSectionType.inventory:
        sectionScreen = InventorySection(
          formBloc: _formBloc!,
          initialData: currentData,
        );
        break;
      case ProductFormSectionType.swadeshiLabels:
        sectionScreen = SwadeshiLabelsSection(
          formBloc: _formBloc!,
          initialData: currentData,
        );
        break;
      default:
        CommonMethods.toastMessage(
          '${sectionType.displayName} section coming soon...',
          context,
        );
        return;
    }

    if (sectionScreen != null) {
      final result = await Navigator.push(
        context,
        MaterialPageRoute(builder: (context) => sectionScreen!),
      );

      // Handle the result from the section screen
      if (result != null) {
        debugPrint('UnifiedProductManagementScreen: Section result: $result');
        _handleSectionResult(sectionType.displayName, result);
      }
    }
  }

  void _handleSectionResult(String sectionTitle, dynamic result) {
    setState(() {
      switch (sectionTitle) {
        case 'Inventory':
          if (result is Map<String, dynamic>) {
            _formData['inventory'] = {
              'options': result['options'] ?? <String, List<String>>{},
              'variants': result['variants'] ?? <Map<String, dynamic>>[],
              'hasMultipleOptions': result['hasMultipleOptions'] ?? false,
            };
            debugPrint(
                'UnifiedProductManagementScreen: Inventory data saved: ${_formData['inventory']}');
          }
          break;
        case 'Swadeshi labels':
          if (result is Product) {
            _formData['swadeshi_labels'] = result;
            debugPrint(
                'UnifiedProductManagementScreen: Swadeshi labels saved: ${result.swadeshiBrand}, ${result.swadeshiMade}, ${result.swadeshiOwned}');
          }
          break;
        case 'Delivery settings':
          // Handle delivery settings result
          _formData['delivery_settings'] = result;
          debugPrint(
              'UnifiedProductManagementScreen: Delivery settings saved: $result');
          break;
        case 'Return settings':
          // Handle return settings result
          _formData['return_settings'] = result;
          debugPrint(
              'UnifiedProductManagementScreen: Return settings saved: $result');
          break;
        default:
          debugPrint(
              'UnifiedProductManagementScreen: Unhandled section result for $sectionTitle: $result');
          break;
      }
    });
  }

  void _onTapAppBarAction() {
    if (_formBloc?.hasUnsavedChanges == true) {
      // Save draft functionality
      CommonMethods.toastMessage('Draft saved', context);
    } else {
      // Reset functionality
      _showResetConfirmation();
    }
  }

  void _showResetConfirmation() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Reset Form'),
        content: Text('Are you sure you want to reset all changes?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // Reset form logic here
              CommonMethods.toastMessage('Form reset', context);
            },
            child: Text('Reset'),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
          ),
        ],
      ),
    );
  }

  void _onTapPreview() {
    if (_formBloc == null) return;

    // Validate form one more time
    final validationResult = _formBloc!.validateForm();

    if (validationResult.isValid) {
      // Navigate to preview screen
      CommonMethods.toastMessage('Navigating to preview...', context);
    } else {
      // Show validation errors
      CommonMethods.toastMessage(
        'Please resolve all issues before proceeding',
        context,
      );
    }
  }

  Future<bool> _onWillPop() async {
    if (_formBloc?.hasUnsavedChanges == true) {
      final shouldPop = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: Text('Unsaved Changes'),
          content:
              Text('You have unsaved changes. Are you sure you want to leave?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context, false),
              child: Text('Stay'),
            ),
            TextButton(
              onPressed: () => Navigator.pop(context, true),
              child: Text('Leave'),
              style: TextButton.styleFrom(foregroundColor: Colors.red),
            ),
          ],
        ),
      );
      return shouldPop ?? false;
    }
    return true;
  }
}

// Separate form screen classes for data persistence
class _BasicDetailsFormScreen extends StatefulWidget {
  final Map<String, dynamic> initialData;
  final Function(Map<String, dynamic>) onSave;

  const _BasicDetailsFormScreen({
    Key? key,
    required this.initialData,
    required this.onSave,
  }) : super(key: key);

  @override
  State<_BasicDetailsFormScreen> createState() =>
      _BasicDetailsFormScreenState();
}

class _BasicDetailsFormScreenState extends State<_BasicDetailsFormScreen> {
  late TextEditingController _productNameController;
  late TextEditingController _brandNameController;
  late TextEditingController _categoryController;
  late TextEditingController _descriptionController;

  @override
  void initState() {
    super.initState();
    _productNameController =
        TextEditingController(text: widget.initialData['product_name'] ?? '');
    _brandNameController =
        TextEditingController(text: widget.initialData['brand_name'] ?? '');
    _categoryController =
        TextEditingController(text: widget.initialData['category'] ?? '');
    _descriptionController =
        TextEditingController(text: widget.initialData['description'] ?? '');
  }

  @override
  void dispose() {
    _productNameController.dispose();
    _brandNameController.dispose();
    _categoryController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  void _saveData() {
    final data = {
      'product_name': _productNameController.text,
      'brand_name': _brandNameController.text,
      'category': _categoryController.text,
      'description': _descriptionController.text,
    };

    widget.onSave(data);
    Navigator.pop(context, data);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Basic Details'),
        backgroundColor: AppColors.appWhite,
        foregroundColor: AppColors.appBlack,
        elevation: 0,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Basic Details',
              style:
                  AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
            ),
            verticalSizedBox(16),
            TextField(
              controller: _productNameController,
              decoration: const InputDecoration(
                labelText: 'Product Name',
                hintText: 'Enter product name',
                border: OutlineInputBorder(),
              ),
            ),
            verticalSizedBox(16),
            TextField(
              controller: _brandNameController,
              decoration: const InputDecoration(
                labelText: 'Brand Name',
                hintText: 'Enter brand name',
                border: OutlineInputBorder(),
              ),
            ),
            verticalSizedBox(16),
            TextField(
              controller: _categoryController,
              decoration: const InputDecoration(
                labelText: 'Product Category',
                hintText: 'Select product category',
                border: OutlineInputBorder(),
              ),
            ),
            verticalSizedBox(16),
            TextField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                labelText: 'Product Description',
                hintText: 'Enter product description',
                border: OutlineInputBorder(),
              ),
              maxLines: 4,
            ),
            const Spacer(),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _saveData,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.brandBlack,
                  foregroundColor: AppColors.appWhite,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: const Text('Save Basic Details'),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _ProductPromotionsFormScreen extends StatefulWidget {
  final Map<String, dynamic> initialData;
  final Function(Map<String, dynamic>) onSave;

  const _ProductPromotionsFormScreen({
    Key? key,
    required this.initialData,
    required this.onSave,
  }) : super(key: key);

  @override
  State<_ProductPromotionsFormScreen> createState() =>
      _ProductPromotionsFormScreenState();
}

class _ProductPromotionsFormScreenState
    extends State<_ProductPromotionsFormScreen> {
  late TextEditingController _amountController;
  late bool _promotionsEnabled;

  @override
  void initState() {
    super.initState();
    _promotionsEnabled = widget.initialData['enabled'] ?? false;
    _amountController = TextEditingController(
        text: (widget.initialData['amount'] ?? 0).toString());
  }

  @override
  void dispose() {
    _amountController.dispose();
    super.dispose();
  }

  void _saveData() {
    final data = {
      'enabled': _promotionsEnabled,
      'amount': int.tryParse(_amountController.text) ?? 0,
    };

    widget.onSave(data);
    Navigator.pop(context, data);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Product Promotions'),
        backgroundColor: AppColors.appWhite,
        foregroundColor: AppColors.appBlack,
        elevation: 0,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Product Promotions',
              style:
                  AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
            ),
            verticalSizedBox(16),
            CheckboxListTile(
              title: const Text('Enable Product Promotions'),
              value: _promotionsEnabled,
              onChanged: (value) {
                setState(() {
                  _promotionsEnabled = value ?? false;
                });
              },
            ),
            verticalSizedBox(16),
            TextField(
              controller: _amountController,
              decoration: const InputDecoration(
                labelText: 'Promotional Amount',
                hintText: 'Enter promotional amount',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.number,
              enabled: _promotionsEnabled,
            ),
            const Spacer(),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _saveData,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.brandBlack,
                  foregroundColor: AppColors.appWhite,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: const Text('Save Promotions'),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _VisibilityFormScreen extends StatefulWidget {
  final Map<String, dynamic> initialData;
  final Function(Map<String, dynamic>) onSave;

  const _VisibilityFormScreen({
    Key? key,
    required this.initialData,
    required this.onSave,
  }) : super(key: key);

  @override
  State<_VisibilityFormScreen> createState() => _VisibilityFormScreenState();
}

class _VisibilityFormScreenState extends State<_VisibilityFormScreen> {
  late TextEditingController _slugController;
  late TextEditingController _codeController;
  late TextEditingController _hashtagsController;

  @override
  void initState() {
    super.initState();
    _slugController =
        TextEditingController(text: widget.initialData['slug'] ?? '');
    _codeController =
        TextEditingController(text: widget.initialData['code'] ?? '');
    _hashtagsController =
        TextEditingController(text: widget.initialData['hashtags'] ?? '');
  }

  @override
  void dispose() {
    _slugController.dispose();
    _codeController.dispose();
    _hashtagsController.dispose();
    super.dispose();
  }

  void _saveData() {
    final data = {
      'slug': _slugController.text,
      'code': _codeController.text,
      'hashtags': _hashtagsController.text,
    };

    widget.onSave(data);
    Navigator.pop(context, data);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Visibility Settings'),
        backgroundColor: AppColors.appWhite,
        foregroundColor: AppColors.appBlack,
        elevation: 0,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Visibility Settings',
              style:
                  AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
            ),
            verticalSizedBox(16),
            TextField(
              controller: _slugController,
              decoration: const InputDecoration(
                labelText: 'Product Slug',
                hintText: 'Auto-filled from product name',
                border: OutlineInputBorder(),
              ),
            ),
            verticalSizedBox(16),
            TextField(
              controller: _codeController,
              decoration: const InputDecoration(
                labelText: 'Product Code',
                hintText: 'Enter product code',
                border: OutlineInputBorder(),
              ),
            ),
            verticalSizedBox(16),
            TextField(
              controller: _hashtagsController,
              decoration: const InputDecoration(
                labelText: 'Product Hashtags',
                hintText: 'Enter hashtags separated by commas',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
            const Spacer(),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _saveData,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.brandBlack,
                  foregroundColor: AppColors.appWhite,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: const Text('Save Visibility Settings'),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _MoreDetailsFormScreen extends StatefulWidget {
  final Map<String, dynamic> initialData;
  final Function(Map<String, dynamic>) onSave;

  const _MoreDetailsFormScreen({
    Key? key,
    required this.initialData,
    required this.onSave,
  }) : super(key: key);

  @override
  State<_MoreDetailsFormScreen> createState() => _MoreDetailsFormScreenState();
}

class _MoreDetailsFormScreenState extends State<_MoreDetailsFormScreen> {
  late TextEditingController _promotionLinkController;

  @override
  void initState() {
    super.initState();
    _promotionLinkController =
        TextEditingController(text: widget.initialData['promotion_link'] ?? '');
  }

  @override
  void dispose() {
    _promotionLinkController.dispose();
    super.dispose();
  }

  void _saveData() {
    final data = {
      'promotion_link': _promotionLinkController.text,
    };

    widget.onSave(data);
    Navigator.pop(context, data);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('More Details'),
        backgroundColor: AppColors.appWhite,
        foregroundColor: AppColors.appBlack,
        elevation: 0,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'More Details',
              style:
                  AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
            ),
            verticalSizedBox(16),
            TextField(
              controller: _promotionLinkController,
              decoration: const InputDecoration(
                labelText: 'Promotion Link',
                hintText: 'Enter promotion link URL',
                border: OutlineInputBorder(),
              ),
            ),
            const Spacer(),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _saveData,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.brandBlack,
                  foregroundColor: AppColors.appWhite,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: const Text('Save More Details'),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
