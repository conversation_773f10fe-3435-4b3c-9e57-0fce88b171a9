import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:swadesic/model/unified_product_image_state/unified_product_image_state.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

/// Unified image manager for both add and edit modes
class ProductImageManager extends StatefulWidget {
  final UnifiedProductImageState imageState;
  final Function(UnifiedProductImageState) onImageStateChanged;
  final int maxImages;
  final bool isEditMode;

  const ProductImageManager({
    Key? key,
    required this.imageState,
    required this.onImageStateChanged,
    this.maxImages = 10,
    required this.isEditMode,
  }) : super(key: key);

  @override
  State<ProductImageManager> createState() => _ProductImageManagerState();
}

class _ProductImageManagerState extends State<ProductImageManager> {
  final ImagePicker _imagePicker = ImagePicker();
  late UnifiedProductImageState _currentImageState;

  @override
  void initState() {
    super.initState();
    _currentImageState = widget.imageState;
  }

  @override
  void didUpdateWidget(ProductImageManager oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.imageState != widget.imageState) {
      _currentImageState = widget.imageState;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildHeader(),
        verticalSizedBox(15),
        _buildImageGallery(),
        if (_currentImageState.hasImages) ...[
          verticalSizedBox(10),
          _buildImageActions(),
        ],
      ],
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Text(
          AppStrings.addProductImage,
          style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
        ),
        horizontalSizedBox(8),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
          decoration: BoxDecoration(
            color: AppColors.brandBlack,
            borderRadius: BorderRadius.circular(10),
          ),
          child: Text(
            '${_currentImageState.totalImageCount}/${widget.maxImages}',
            style: AppTextStyle.smallText(textColor: AppColors.appWhite),
          ),
        ),
        const Spacer(),
        if (!_currentImageState.hasReachedLimit(widget.maxImages))
          _buildAddImageButton(),
      ],
    );
  }

  Widget _buildAddImageButton() {
    return GestureDetector(
      onTap: _showImagePickerOptions,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          border: Border.all(color: AppColors.brandBlack),
          borderRadius: BorderRadius.circular(20),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.add_photo_alternate_outlined,
              size: 16,
              color: AppColors.brandBlack,
            ),
            horizontalSizedBox(4),
            Text(
              'Add Images',
              style: AppTextStyle.smallText(textColor: AppColors.brandBlack),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImageGallery() {
    if (!_currentImageState.hasImages) {
      return _buildEmptyState();
    }

    return Container(
      height: 120,
      child: ReorderableListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: _currentImageState.displayItems.length,
        onReorder: _onReorderImages,
        itemBuilder: (context, index) {
          final item = _currentImageState.displayItems[index];
          return _buildImageItem(item, index);
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    return GestureDetector(
      onTap: _showImagePickerOptions,
      child: Container(
        height: 120,
        width: double.infinity,
        decoration: BoxDecoration(
          border: Border.all(
            color: AppColors.borderColor1,
            style: BorderStyle.solid,
          ),
          borderRadius: BorderRadius.circular(8),
          color: AppColors.textFieldFill1,
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.add_photo_alternate_outlined,
              size: 40,
              color: AppColors.writingColor2,
            ),
            verticalSizedBox(8),
            Text(
              'Add Product Images',
              style: AppTextStyle.contentText0(textColor: AppColors.writingColor2),
            ),
            verticalSizedBox(4),
            Text(
              'Tap to add images (Max ${widget.maxImages})',
              style: AppTextStyle.smallText(textColor: AppColors.writingColor2),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImageItem(ImageDisplayItem item, int index) {
    return Container(
      key: ValueKey(item.id),
      margin: const EdgeInsets.only(right: 8),
      child: Stack(
        children: [
          _buildImageContainer(item),
          _buildRemoveButton(item),
          if (index == 0) _buildPrimaryBadge(),
        ],
      ),
    );
  }

  Widget _buildImageContainer(ImageDisplayItem item) {
    return Container(
      width: 120,
      height: 120,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.borderColor1),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: _buildImageWidget(item),
      ),
    );
  }

  Widget _buildImageWidget(ImageDisplayItem item) {
    if (item.isNetworkImage) {
      return Image.network(
        item.imageUrl,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          return Container(
            color: AppColors.textFieldFill1,
            child: Icon(
              Icons.broken_image,
              color: AppColors.writingColor2,
            ),
          );
        },
        loadingBuilder: (context, child, loadingProgress) {
          if (loadingProgress == null) return child;
          return Container(
            color: AppColors.textFieldFill1,
            child: Center(
              child: CircularProgressIndicator(
                value: loadingProgress.expectedTotalBytes != null
                    ? loadingProgress.cumulativeBytesLoaded /
                        loadingProgress.expectedTotalBytes!
                    : null,
                color: AppColors.brandBlack,
              ),
            ),
          );
        },
      );
    } else if (item.isFileImage) {
      if (kIsWeb) {
        // For web, use Image.network with the file path
        return Image.network(
          item.imageUrl,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) {
            return Container(
              color: AppColors.textFieldFill1,
              child: Icon(
                Icons.broken_image,
                color: AppColors.writingColor2,
              ),
            );
          },
        );
      } else {
        // For mobile, use Image.file
        return Image.file(
          File(item.imageUrl),
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) {
            return Container(
              color: AppColors.textFieldFill1,
              child: Icon(
                Icons.broken_image,
                color: AppColors.writingColor2,
              ),
            );
          },
        );
      }
    }

    return Container(
      color: AppColors.textFieldFill1,
      child: Icon(
        Icons.image,
        color: AppColors.writingColor2,
      ),
    );
  }

  Widget _buildRemoveButton(ImageDisplayItem item) {
    return Positioned(
      top: 4,
      right: 4,
      child: GestureDetector(
        onTap: () => _removeImage(item.id),
        child: Container(
          width: 24,
          height: 24,
          decoration: BoxDecoration(
            color: AppColors.appWhite,
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.2),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Icon(
            Icons.close,
            size: 16,
            color: AppColors.brandBlack,
          ),
        ),
      ),
    );
  }

  Widget _buildPrimaryBadge() {
    return Positioned(
      bottom: 4,
      left: 4,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
        decoration: BoxDecoration(
          color: AppColors.brandBlack,
          borderRadius: BorderRadius.circular(4),
        ),
        child: Text(
          'Primary',
          style: AppTextStyle.smallText(textColor: AppColors.appWhite),
        ),
      ),
    );
  }

  Widget _buildImageActions() {
    return Row(
      children: [
        if (_currentImageState.hasUnsavedChanges) ...[
          TextButton.icon(
            onPressed: _resetImages,
            icon: Icon(Icons.refresh, size: 16),
            label: Text('Reset'),
            style: TextButton.styleFrom(
              foregroundColor: AppColors.writingColor2,
            ),
          ),
          horizontalSizedBox(8),
        ],
        TextButton.icon(
          onPressed: _clearAllImages,
          icon: Icon(Icons.delete_outline, size: 16),
          label: Text('Clear All'),
          style: TextButton.styleFrom(
            foregroundColor: Colors.red,
          ),
        ),
      ],
    );
  }

  void _showImagePickerOptions() {
    showModalBottomSheet(
      context: context,
      builder: (context) => SafeArea(
        child: Wrap(
          children: [
            ListTile(
              leading: Icon(Icons.photo_library),
              title: Text('Choose from Gallery'),
              onTap: () {
                Navigator.pop(context);
                _pickImages(ImageSource.gallery);
              },
            ),
            ListTile(
              leading: Icon(Icons.photo_camera),
              title: Text('Take Photo'),
              onTap: () {
                Navigator.pop(context);
                _pickImages(ImageSource.camera);
              },
            ),
            if (!_currentImageState.hasReachedLimit(widget.maxImages - 1))
              ListTile(
                leading: Icon(Icons.photo_library_outlined),
                title: Text('Choose Multiple'),
                onTap: () {
                  Navigator.pop(context);
                  _pickMultipleImages();
                },
              ),
          ],
        ),
      ),
    );
  }

  Future<void> _pickImages(ImageSource source) async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: source,
        maxWidth: 1920,
        maxHeight: 1920,
        imageQuality: 85,
      );

      if (image != null) {
        _addImage(image);
      }
    } catch (e) {
      if (context.mounted) {
        CommonMethods.toastMessage(
          'Failed to pick image: ${e.toString()}',
          context,
        );
      }
    }
  }

  Future<void> _pickMultipleImages() async {
    try {
      final remainingSlots = widget.maxImages - _currentImageState.totalImageCount;
      
      final List<XFile> images = await _imagePicker.pickMultiImage(
        maxWidth: 1920,
        maxHeight: 1920,
        imageQuality: 85,
      );

      if (images.isNotEmpty) {
        final imagesToAdd = images.take(remainingSlots).toList();
        _addMultipleImages(imagesToAdd);
        
        if (images.length > remainingSlots) {
          if (context.mounted) {
            CommonMethods.toastMessage(
              'Only ${remainingSlots} images were added due to limit',
              context,
            );
          }
        }
      }
    } catch (e) {
      if (context.mounted) {
        CommonMethods.toastMessage(
          'Failed to pick images: ${e.toString()}',
          context,
        );
      }
    }
  }

  void _addImage(XFile image) {
    final newState = _currentImageState.copyWith();
    newState.addNewImage(image);
    _updateImageState(newState);
  }

  void _addMultipleImages(List<XFile> images) {
    final newState = _currentImageState.copyWith();
    newState.addNewImages(images);
    _updateImageState(newState);
  }

  void _removeImage(String imageId) {
    final newState = _currentImageState.copyWith();
    newState.removeImage(imageId);
    _updateImageState(newState);
  }

  void _onReorderImages(int oldIndex, int newIndex) {
    if (oldIndex < newIndex) {
      newIndex -= 1;
    }

    final items = List<ImageDisplayItem>.from(_currentImageState.displayItems);
    final item = items.removeAt(oldIndex);
    items.insert(newIndex, item);

    final newOrder = items.map((item) => item.id).toList();
    final newState = _currentImageState.copyWith();
    newState.reorderImages(newOrder);
    _updateImageState(newState);
  }

  void _resetImages() {
    final newState = _currentImageState.copyWith();
    newState.resetToOriginal();
    _updateImageState(newState);
  }

  void _clearAllImages() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Clear All Images'),
        content: Text('Are you sure you want to remove all images?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              final newState = _currentImageState.copyWith();
              newState.clearAllImages();
              _updateImageState(newState);
            },
            child: Text('Clear All'),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
          ),
        ],
      ),
    );
  }

  void _updateImageState(UnifiedProductImageState newState) {
    setState(() {
      _currentImageState = newState;
    });
    widget.onImageStateChanged(newState);
  }
}
