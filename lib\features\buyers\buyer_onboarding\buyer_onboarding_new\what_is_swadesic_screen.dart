import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/buyers/buyer_onboarding/buyer_onboarding_new/initial_onboarding_screen.dart';
import 'package:swadesic/features/buyers/buyer_onboarding/buyer_onboarding_new/what_is_swadesic_bloc.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_text_style.dart';

class WhatIsSwadesicScreen extends StatefulWidget {
  final String userReference;
  final Map<String, dynamic> userData;
  final String? icon;

  const WhatIsSwadesicScreen({
    Key? key,
    required this.userReference,
    required this.userData,
    this.icon,
  }) : super(key: key);

  @override
  _WhatIsSwadesicScreenState createState() => _WhatIsSwadesicScreenState();
}

class _WhatIsSwadesicScreenState extends State<WhatIsSwadesicScreen> {
  //region Bloc
  late WhatIsSwadesicBloc whatIsSwadesicBloc;

  //endregion

  //region Init
  @override
  void initState() {
    whatIsSwadesicBloc = WhatIsSwadesicBloc(context);
    super.initState();
  }

  //endregion

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.appWhite,
      body: SafeArea(
        child: body(),
      ),
    );
  }

  Widget body() {
    return LayoutBuilder(
      builder: (context, constraints) {
        return SingleChildScrollView(
          child: ConstrainedBox(
            constraints: BoxConstraints(minHeight: constraints.maxHeight),
            child: IntrinsicHeight(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 24),
                child: Column(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Spacer(),
                    const SizedBox(height: 40),
                    header(),
                    const SizedBox(height: 30),
                    platformDescription(),
                    const SizedBox(height: 40),
                    swadesicMission(),
                    const SizedBox(height: 10),
                    swadesicPlayBook(),
                    const SizedBox(height: 40),
                    const Spacer(),
                    areYouIn(),
                    const SizedBox(height: 15),
                    imInButton(),
                    const SizedBox(height: 40),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  //region Header
  Widget header() {
    return Row(
      children: [
        Image.asset(
          AppImages.appIcon,
          height: 50,
          width: 50,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            "Swadesic is a Community on Mission",
            style: AppTextStyle.exHeading1(textColor: AppColors.appBlack),
          ),
        ),
      ],
    );
  }
  //endregion

  //region Platform Description
  Widget platformDescription() {
    return Text(
      "We are Bharat's first Direct-from-Store Shopping Network. Your every action is designed to support small business owners & consumers of the nation.",
      style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
    );
  }
  //endregion

  Widget swadesicPlayBook() {
    return FAQItem(
      question: "What's the Playbook to achieve the Mission?",
      answer:
          "Swadesic transforms commerce from a platform-controlled economy into a people-owned ecosystem. It does this by:\n\n1. Giving every store a free (permanent) digital presence with tools to grow their business, restoring ownership and reach.\n\n2. Plugging every store into a national discovery network where buyers can discover and support them directly — without middlemen.\n\n3. Enabling community-powered branding and orders, turning buyers into supporters, not just consumers.\n\nAnd All possible things we can do to support small businesses and consumers.",
    );
  }

  Widget swadesicMission() {
    return FAQItem(
      question: "What is Swadesic Mission?",
      answer:
          "To ensure national markets stay sovereign from foreign control — by restoring ownership to those who live, build, and buy within the nations.",
    );
  }

  //region Are You In
  Widget areYouIn() {
    return Text(
      "Are you in?",
      style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
    );
  }
  //endregion

  //region I'm In Button
  Widget imInButton() {
    return SizedBox(
      width: double.infinity,
      child: CupertinoButton(
        borderRadius: BorderRadius.circular(11),
        padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 15),
        color: AppColors.appBlack,
        child: Text(
          "Yes, I am.",
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
          style: AppTextStyle.access0(textColor: AppColors.appWhite),
        ),
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => InitialOnboardingScreen(
                userReference: widget.userReference,
                icon: widget.icon,
              ),
            ),
          );
        },
      ),
    );
  }
  //endregion
}

//region FAQ Widget
class FAQItem extends StatefulWidget {
  final String question;
  final String answer;

  const FAQItem({
    Key? key,
    required this.question,
    required this.answer,
  }) : super(key: key);

  @override
  State<FAQItem> createState() => _FAQItemState();
}

class _FAQItemState extends State<FAQItem> {
  bool isExpanded = false;

  @override
  Widget build(BuildContext context) {
    return ExpansionTile(
      title: Text(
        widget.question,
        style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
      ),
      trailing: Icon(
        isExpanded ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
        color: AppColors.appBlack,
      ),
      onExpansionChanged: (expanded) {
        setState(() => isExpanded = expanded);
      },
      collapsedShape: const Border(),
      shape: const Border(),
      tilePadding: EdgeInsets.only(top: 10),
      children: [
        Text(
          widget.answer,
          style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
        ),
      ],
    );
  }
}
//endregion
