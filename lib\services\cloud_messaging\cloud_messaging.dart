import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:swadesic/features/notification/notification_bloc.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/services/cloud_messaging/on_open_notification.dart';
import 'package:swadesic/services/user_device_detail_services/user_device_detail_service.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';

class CloudMessaging {
  FirebaseMessaging messaging = FirebaseMessaging.instance;





  //region Initialization messaging
  Future<void> initMessaging() async {
    NotificationSettings settings = await messaging.requestPermission(
      alert: true,
      announcement: false,
      badge: true,
      carPlay: false,
      criticalAlert: false,
      provisional: false,
      sound: true,
    );
    onOpenBackGroundNotification();
    saveDeviceIdAndFcmToken();
    onOpenClosedNotification();
    onRefreshToken();
  }
//endregion



  //region Save device id and fcm
  Future<void> saveDeviceIdAndFcmToken() async {
    //region Try
    try {
      //If web or static user view then ignore
      if(kIsWeb || AppConstants.appData.userReference == AppConstants.staticUser){
        return;
      }
      //Check is the device token already exist or not
      String deviceID = await CommonMethods.getDeviceId();
      //Check is the device token is create or not
      bool isExist =
      await UserDeviceDetailService().checkDeviceIdAndFcmCreated(deviceId: await CommonMethods.getDeviceId(), fcmToken: await getToken());
      //If isExist is true then call edit
      if (isExist) {
        await UserDeviceDetailService().editUserDeviceAndFcmDetail(deviceId: await CommonMethods.getDeviceId(), fcmToken: await getToken());
      }
      //If isExist is false then call add
      else {
        await UserDeviceDetailService().addUserDeviceAndFcmDetail(deviceId: await CommonMethods.getDeviceId(), fcmToken: await getToken());
      }
    }
    //endregion
    on ApiErrorResponseMessage catch (error) {
      //print(error);

      // CommonMethods.toastMessage(error.message!, AppConstants.globalNavigator.currentContext);
    } catch (error) {
      //print(error);
      // CommonMethods.toastMessage(AppStrings.commonErrorMessage, AppConstants.globalNavigator.currentContext);
    }
  }

  //endregion

  //region On refresh token
  void onRefreshToken(){
    FirebaseMessaging.instance.onTokenRefresh
        .listen((fcmToken) {
      saveDeviceIdAndFcmToken();
    })
        .onError((err) {
      // Error getting token.
    });
  }
  //endregion

  //region Token
  Future<String> getToken() async {
    String? token = await messaging.getToken();
    //print("FCM token is ${token}");
    return token!;
  }

//endregion

//region On open back ground notification
  void onOpenBackGroundNotification() async {
    print("FCM: Setting up background notification listener");

    /// on click Notification from background (warm start)
    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
      print("FCM: Background notification tapped - warm start");
      print("FCM: Message data: ${message.data}");

      //If notification is not null
      if(message.notification != null){
        print("FCM: Processing warm start notification");
        OnOpenAppNotification().bottomNavigationMountedCheck(notification: message);
      } else {
        print("FCM: Notification object is null in warm start");
      }
    }, onError: (error) {
      print("FCM: Error in background notification listener: $error");
    });
  }
//endregion



//region On open closed notification
  void onOpenClosedNotification() async {
    print("FCM: Setting up cold start notification handler");

    /// on click Notification from cold start (app was terminated)
    FirebaseMessaging.instance.getInitialMessage().then((notificationMessage){
      print("FCM: Checking for initial message from cold start");

      //If notification message is null then return
      if(notificationMessage == null){
        print("FCM: No initial message found");
        return;
      }

      print("FCM: Cold start notification found");
      print("FCM: Message data: ${notificationMessage.data}");

      //If notification is not null
      if(notificationMessage.notification != null){
        print("FCM: Processing cold start notification");
        OnOpenAppNotification().bottomNavigationMountedCheck(notification: notificationMessage);
      } else {
        print("FCM: Notification object is null in cold start");
      }
    }).catchError((error) {
      print("FCM: Error getting initial message: $error");
    });
  }
//endregion





}
