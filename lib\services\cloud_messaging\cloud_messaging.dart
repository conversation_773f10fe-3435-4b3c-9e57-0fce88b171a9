import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:swadesic/features/notification/notification_bloc.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/services/cloud_messaging/on_open_notification.dart';
import 'package:swadesic/services/user_device_detail_services/user_device_detail_service.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';

class CloudMessaging {
  FirebaseMessaging messaging = FirebaseMessaging.instance;





  //region Initialization messaging
  Future<void> initMessaging() async {
    NotificationSettings settings = await messaging.requestPermission(
      alert: true,
      announcement: false,
      badge: true,
      carPlay: false,
      criticalAlert: false,
      provisional: false,
      sound: true,
    );
    onOpenBackGroundNotification();
    saveDeviceIdAndFcmToken();
    onOpenClosedNotification();
    onRefreshToken();
  }
//endregion



  //region Save device id and fcm
  Future<void> saveDeviceIdAndFcmToken() async {
    //region Try
    try {
      //If web or static user view then ignore
      if(kIsWeb || AppConstants.appData.userReference == AppConstants.staticUser){
        return;
      }
      //Check is the device token already exist or not
      String deviceID = await CommonMethods.getDeviceId();
      //Check is the device token is create or not
      bool isExist =
      await UserDeviceDetailService().checkDeviceIdAndFcmCreated(deviceId: await CommonMethods.getDeviceId(), fcmToken: await getToken());
      //If isExist is true then call edit
      if (isExist) {
        await UserDeviceDetailService().editUserDeviceAndFcmDetail(deviceId: await CommonMethods.getDeviceId(), fcmToken: await getToken());
      }
      //If isExist is false then call add
      else {
        await UserDeviceDetailService().addUserDeviceAndFcmDetail(deviceId: await CommonMethods.getDeviceId(), fcmToken: await getToken());
      }
    }
    //endregion
    on ApiErrorResponseMessage catch (error) {
      //print(error);

      // CommonMethods.toastMessage(error.message!, AppConstants.globalNavigator.currentContext);
    } catch (error) {
      //print(error);
      // CommonMethods.toastMessage(AppStrings.commonErrorMessage, AppConstants.globalNavigator.currentContext);
    }
  }

  //endregion

  //region On refresh token
  void onRefreshToken(){
    FirebaseMessaging.instance.onTokenRefresh
        .listen((fcmToken) {
      saveDeviceIdAndFcmToken();
    })
        .onError((err) {
      // Error getting token.
    });
  }
  //endregion

  //region Token
  Future<String> getToken() async {
    String? token = await messaging.getToken();
    //print("FCM token is ${token}");
    return token!;
  }

//endregion

//region On open back ground notification
  void onOpenBackGroundNotification() async {
    /// on click Notification from background
    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
      //If notification is not null
      if(message.notification != null){
        //print("Notification in tapped");
        OnOpenAppNotification().bottomNavigationMountedCheck(notification: message);
      }

    });
  }
//endregion



//region On open closed notification
  void onOpenClosedNotification() async {
    /// on click Notification from background
    FirebaseMessaging.instance.getInitialMessage().then((notificationMessage){
      //print("Notification in tapped from terminate state");

      RemoteMessage? message = const RemoteMessage();
      //If notification message is null then return
      if(notificationMessage == null){
        return;
      }
      message = notificationMessage;
      //If notification is not null
      if(message.notification != null){
        //print("Notification in tapped from terminate state");
        OnOpenAppNotification().bottomNavigationMountedCheck(notification: message);
      }

    });
  }
//endregion





}
