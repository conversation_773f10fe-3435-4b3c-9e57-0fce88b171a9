import 'package:flutter/material.dart';
import 'package:swadesic/features/seller/unified_product_management/unified_product_form_bloc.dart';
import 'package:swadesic/features/seller/unified_product_management/models/product_form_section.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

/// Basic Details section for product form
class BasicDetailsSection extends StatefulWidget {
  final UnifiedProductFormBloc formBloc;
  final Map<String, dynamic> initialData;

  const BasicDetailsSection({
    Key? key,
    required this.formBloc,
    required this.initialData,
  }) : super(key: key);

  @override
  State<BasicDetailsSection> createState() => _BasicDetailsSectionState();
}

class _BasicDetailsSectionState extends State<BasicDetailsSection> {
  late TextEditingController _productNameController;
  late TextEditingController _brandNameController;
  late TextEditingController _productCategoryController;
  late TextEditingController _productDescriptionController;

  final _formKey = GlobalKey<FormState>();
  bool _hasChanges = false;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
  }

  void _initializeControllers() {
    _productNameController = TextEditingController(
      text: widget.initialData['productName']?.toString() ?? '',
    );
    _brandNameController = TextEditingController(
      text: widget.initialData['brandName']?.toString() ?? '',
    );
    _productCategoryController = TextEditingController(
      text: widget.initialData['productCategory']?.toString() ?? '',
    );
    _productDescriptionController = TextEditingController(
      text: widget.initialData['productDescription']?.toString() ?? '',
    );

    // Add listeners to track changes
    _productNameController.addListener(_onFieldChanged);
    _brandNameController.addListener(_onFieldChanged);
    _productCategoryController.addListener(_onFieldChanged);
    _productDescriptionController.addListener(_onFieldChanged);
  }

  void _onFieldChanged() {
    if (!_hasChanges) {
      setState(() {
        _hasChanges = true;
      });
    }
  }

  @override
  void dispose() {
    _productNameController.dispose();
    _brandNameController.dispose();
    _productCategoryController.dispose();
    _productDescriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: _onWillPop,
      child: GestureDetector(
        onTap: () => CommonMethods.closeKeyboard(context),
        child: Scaffold(
          backgroundColor: AppColors.appWhite,
          appBar: _buildAppBar(),
          body: _buildBody(),
        ),
      ),
    );
  }

  AppBar _buildAppBar() {
    return AppCommonWidgets.mainAppBar(
      context: context,
      isCustomTitle: false,
      title: 'Basic Details',
      isDefaultMenuVisible: false,
      isMembershipVisible: false,
      isCartVisible: false,
      isTextButtonVisible: true,
      textButtonWidget: AppCommonWidgets.appBarTextButtonText(
        text: AppStrings.save,
      ),
      onTapTextButton: _onTapSave,
    );
  }

  Widget _buildBody() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSectionHeader(),
            verticalSizedBox(24),
            _buildProductNameField(),
            verticalSizedBox(20),
            _buildBrandNameField(),
            verticalSizedBox(20),
            _buildProductCategoryField(),
            verticalSizedBox(20),
            _buildProductDescriptionField(),
            verticalSizedBox(32),
            _buildValidationInfo(),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.textFieldFill1,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.info_outline,
                color: AppColors.brandBlack,
                size: 24,
              ),
              horizontalSizedBox(8),
              Text(
                'Basic Product Information',
                style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
              ),
            ],
          ),
          verticalSizedBox(8),
          Text(
            'Provide essential information about your product. All fields marked with * are required.',
            style: AppTextStyle.contentText0(textColor: AppColors.writingColor2),
          ),
        ],
      ),
    );
  }

  Widget _buildProductNameField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildFieldLabel('Product Name', isRequired: true),
        verticalSizedBox(8),
        TextFormField(
          controller: _productNameController,
          decoration: InputDecoration(
            hintText: 'Enter product name',
            filled: true,
            fillColor: AppColors.textFieldFill1,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: AppColors.borderColor1),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: AppColors.borderColor1),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: AppColors.brandBlack, width: 2),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.red),
            ),
          ),
          style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Product name is required';
            }
            if (value.trim().length < 3) {
              return 'Product name must be at least 3 characters';
            }
            return null;
          },
          textCapitalization: TextCapitalization.words,
          maxLength: 100,
        ),
        _buildFieldHint('Choose a clear, descriptive name for your product'),
      ],
    );
  }

  Widget _buildBrandNameField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildFieldLabel('Brand Name', isRequired: true),
        verticalSizedBox(8),
        TextFormField(
          controller: _brandNameController,
          decoration: InputDecoration(
            hintText: 'Enter brand name',
            filled: true,
            fillColor: AppColors.textFieldFill1,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: AppColors.borderColor1),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: AppColors.borderColor1),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: AppColors.brandBlack, width: 2),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.red),
            ),
          ),
          style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Brand name is required';
            }
            return null;
          },
          textCapitalization: TextCapitalization.words,
          maxLength: 50,
        ),
        _buildFieldHint('The brand or manufacturer of this product'),
      ],
    );
  }

  Widget _buildProductCategoryField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildFieldLabel('Product Category', isRequired: true),
        verticalSizedBox(8),
        GestureDetector(
          onTap: _showCategoryPicker,
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
            decoration: BoxDecoration(
              color: AppColors.textFieldFill1,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: AppColors.borderColor1),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    _productCategoryController.text.isEmpty
                        ? 'Select product category'
                        : _productCategoryController.text,
                    style: AppTextStyle.contentText0(
                      textColor: _productCategoryController.text.isEmpty
                          ? AppColors.writingColor2
                          : AppColors.appBlack,
                    ),
                  ),
                ),
                Icon(
                  Icons.arrow_drop_down,
                  color: AppColors.writingColor2,
                ),
              ],
            ),
          ),
        ),
        _buildFieldHint('Choose the most appropriate category for your product'),
      ],
    );
  }

  Widget _buildProductDescriptionField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildFieldLabel('Product Description', isRequired: true),
        verticalSizedBox(8),
        TextFormField(
          controller: _productDescriptionController,
          decoration: InputDecoration(
            hintText: 'Describe your product in detail...',
            filled: true,
            fillColor: AppColors.textFieldFill1,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: AppColors.borderColor1),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: AppColors.borderColor1),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: AppColors.brandBlack, width: 2),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.red),
            ),
            alignLabelWithHint: true,
          ),
          style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Product description is required';
            }
            if (value.trim().length < 20) {
              return 'Description must be at least 20 characters';
            }
            return null;
          },
          maxLines: 5,
          maxLength: 1000,
          textCapitalization: TextCapitalization.sentences,
        ),
        _buildFieldHint('Provide detailed information about features, benefits, and specifications'),
      ],
    );
  }

  Widget _buildFieldLabel(String label, {bool isRequired = false}) {
    return Row(
      children: [
        Text(
          label,
          style: AppTextStyle.contentText0(textColor: AppColors.appBlack)
              .copyWith(fontWeight: FontWeight.w600),
        ),
        if (isRequired) ...[
          horizontalSizedBox(4),
          Text(
            '*',
            style: AppTextStyle.contentText0(textColor: Colors.red),
          ),
        ],
      ],
    );
  }

  Widget _buildFieldHint(String hint) {
    return Padding(
      padding: const EdgeInsets.only(top: 4),
      child: Text(
        hint,
        style: AppTextStyle.smallText(textColor: AppColors.writingColor2),
      ),
    );
  }

  Widget _buildValidationInfo() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.brandBlack.withOpacity(0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.brandBlack.withOpacity(0.1)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.lightbulb_outline,
                color: AppColors.brandBlack,
                size: 20,
              ),
              horizontalSizedBox(8),
              Text(
                'Tips for Better Product Listing',
                style: AppTextStyle.contentText0(textColor: AppColors.appBlack)
                    .copyWith(fontWeight: FontWeight.w600),
              ),
            ],
          ),
          verticalSizedBox(12),
          _buildTip('Use clear, descriptive product names that customers would search for'),
          _buildTip('Include key features and benefits in the description'),
          _buildTip('Choose the most specific category that fits your product'),
          _buildTip('Mention materials, dimensions, or other important specifications'),
        ],
      ),
    );
  }

  Widget _buildTip(String tip) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 4,
            height: 4,
            margin: const EdgeInsets.only(top: 8, right: 8),
            decoration: BoxDecoration(
              color: AppColors.brandBlack,
              shape: BoxShape.circle,
            ),
          ),
          Expanded(
            child: Text(
              tip,
              style: AppTextStyle.smallText(textColor: AppColors.writingColor2),
            ),
          ),
        ],
      ),
    );
  }

  void _showCategoryPicker() {
    // This would show a category picker dialog
    // For now, show a simple list
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Select Category',
              style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
            ),
            verticalSizedBox(16),
            // Sample categories - this would be dynamic in real implementation
            ...[
              'Electronics',
              'Clothing & Fashion',
              'Home & Garden',
              'Health & Beauty',
              'Sports & Outdoors',
              'Books & Media',
              'Toys & Games',
              'Food & Beverages',
            ].map((category) => ListTile(
              title: Text(category),
              onTap: () {
                _productCategoryController.text = category;
                _onFieldChanged();
                Navigator.pop(context);
              },
            )).toList(),
          ],
        ),
      ),
    );
  }

  void _onTapSave() {
    if (_formKey.currentState?.validate() ?? false) {
      final data = {
        'productName': _productNameController.text.trim(),
        'brandName': _brandNameController.text.trim(),
        'productCategory': _productCategoryController.text.trim(),
        'productDescription': _productDescriptionController.text.trim(),
      };

      widget.formBloc.updateSectionData(ProductFormSectionType.basicDetails, data);
      
      CommonMethods.toastMessage('Basic details saved', context);
      Navigator.pop(context, data);
    } else {
      CommonMethods.toastMessage('Please fix the errors above', context);
    }
  }

  Future<bool> _onWillPop() async {
    if (_hasChanges) {
      final shouldPop = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: Text('Unsaved Changes'),
          content: Text('You have unsaved changes. Do you want to save them?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context, false),
              child: Text('Cancel'),
            ),
            TextButton(
              onPressed: () => Navigator.pop(context, true),
              child: Text('Discard'),
              style: TextButton.styleFrom(foregroundColor: Colors.red),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context, false);
                _onTapSave();
              },
              child: Text('Save'),
            ),
          ],
        ),
      );
      return shouldPop ?? false;
    }
    return true;
  }
}
