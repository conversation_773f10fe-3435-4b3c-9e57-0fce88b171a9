import 'package:flutter/material.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:swadesic/services/cloud_messaging/on_open_notification.dart';
import 'package:swadesic/util/app_constants.dart';

/// Debug widget to test notification handling
class NotificationDebugWidget extends StatelessWidget {
  const NotificationDebugWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Text(
            'Notification Debug',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _checkAppState,
            child: const Text('Check App State'),
          ),
          const SizedBox(height: 8),
          ElevatedButton(
            onPressed: _testPostNotification,
            child: const Text('Test Post Notification'),
          ),
          const SizedBox(height: 8),
          ElevatedButton(
            onPressed: _testProductNotification,
            child: const Text('Test Product Notification'),
          ),
          const SizedBox(height: 8),
          ElevatedButton(
            onPressed: _testDirectNavigation,
            child: const Text('Test Direct Navigation'),
          ),
        ],
      ),
    );
  }

  void _checkAppState() {
    print("=== APP STATE CHECK ===");
    print("isBottomNavigationMounted: ${AppConstants.isBottomNavigationMounted.value}");
    print("isUserView: ${AppConstants.appData.isUserView}");
    print("isStoreView: ${AppConstants.appData.isStoreView}");
    print("userReference: ${AppConstants.appData.userReference}");
    
    try {
      print("currentSelectedTabContext available: ${AppConstants.currentSelectedTabContext != null}");
      if (AppConstants.currentSelectedTabContext != null) {
        print("currentSelectedTabContext mounted: ${AppConstants.currentSelectedTabContext.mounted}");
      }
    } catch (e) {
      print("currentSelectedTabContext error: $e");
    }
    
    try {
      print("userStoreCommonBottomNavigationContext available: ${AppConstants.userStoreCommonBottomNavigationContext != null}");
      if (AppConstants.userStoreCommonBottomNavigationContext != null) {
        print("userStoreCommonBottomNavigationContext mounted: ${AppConstants.userStoreCommonBottomNavigationContext.mounted}");
      }
    } catch (e) {
      print("userStoreCommonBottomNavigationContext error: $e");
    }
    
    try {
      final globalContext = AppConstants.globalNavigator.currentContext;
      print("globalNavigator context available: ${globalContext != null}");
      if (globalContext != null) {
        print("globalNavigator context mounted: ${globalContext.mounted}");
      }
    } catch (e) {
      print("globalNavigator context error: $e");
    }
    print("=== END APP STATE CHECK ===");
  }

  void _testPostNotification() {
    print("=== TESTING POST NOTIFICATION ===");
    
    final testMessage = RemoteMessage(
      data: {
        'notification_type': 'standard',
        'notification_about': 'PO1234567890',
        'notified_user_or_store': 'U1234567890',
      },
      notification: const RemoteNotification(
        title: 'Test Post Notification',
        body: 'This is a test post notification',
      ),
    );
    
    try {
      OnOpenAppNotification().bottomNavigationMountedCheck(notification: testMessage);
      print("Post notification test completed");
    } catch (e) {
      print("Error in post notification test: $e");
    }
    print("=== END POST NOTIFICATION TEST ===");
  }

  void _testProductNotification() {
    print("=== TESTING PRODUCT NOTIFICATION ===");
    
    final testMessage = RemoteMessage(
      data: {
        'notification_type': 'standard',
        'notification_about': 'P1234567890',
        'notified_user_or_store': 'U1234567890',
      },
      notification: const RemoteNotification(
        title: 'Test Product Notification',
        body: 'This is a test product notification',
      ),
    );
    
    try {
      OnOpenAppNotification().bottomNavigationMountedCheck(notification: testMessage);
      print("Product notification test completed");
    } catch (e) {
      print("Error in product notification test: $e");
    }
    print("=== END PRODUCT NOTIFICATION TEST ===");
  }

  void _testDirectNavigation() {
    print("=== TESTING DIRECT NAVIGATION ===");
    
    final testMessage = RemoteMessage(
      data: {
        'notification_type': 'standard',
        'notification_about': 'PO1234567890',
        'notified_user_or_store': 'U1234567890',
      },
      notification: const RemoteNotification(
        title: 'Test Direct Navigation',
        body: 'This is a test direct navigation',
      ),
    );
    
    try {
      // Skip the mounted check and go directly to navigation
      OnOpenAppNotification().checkUserOrStoreView(notification: testMessage);
      print("Direct navigation test completed");
    } catch (e) {
      print("Error in direct navigation test: $e");
    }
    print("=== END DIRECT NAVIGATION TEST ===");
  }
}

/// Extension to add debug widget to any screen
extension NotificationDebugExtension on Widget {
  Widget withNotificationDebug() {
    return Column(
      children: [
        Expanded(child: this),
        const NotificationDebugWidget(),
      ],
    );
  }
}
