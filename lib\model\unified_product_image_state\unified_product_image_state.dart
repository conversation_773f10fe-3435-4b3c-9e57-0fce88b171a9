import 'package:image_picker/image_picker.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart';

/// Enhanced image state for unified product management
class UnifiedProductImageState {
  // Existing images from server (for edit mode)
  List<ProdImages> existingImages;

  // New images to be uploaded
  List<XFile> pendingImages;

  // IDs of images marked for deletion
  List<int> deletedImageIds;

  // Ordered structure for display and API
  List<Map<String, dynamic>> orderedStructure;

  // Original order for reset functionality
  List<Map<String, dynamic>>? existingOrderStructure;

  // Change tracking
  bool hasUnsavedChanges;

  // Display items for unified handling
  List<ImageDisplayItem> _displayItems = [];

  UnifiedProductImageState({
    this.existingImages = const [],
    this.pendingImages = const [],
    this.deletedImageIds = const [],
    this.orderedStructure = const [],
    this.existingOrderStructure,
    this.hasUnsavedChanges = false,
  }) {
    _updateDisplayItems();
  }

  // Factory constructor for add mode
  factory UnifiedProductImageState.forAdd() {
    return UnifiedProductImageState();
  }

  // Factory constructor for edit mode
  factory UnifiedProductImageState.forEdit(List<ProdImages> existingImages) {
    final state = UnifiedProductImageState(
      existingImages: existingImages,
      existingOrderStructure: existingImages
          .map((img) => {
                'type': 'existing',
                'id': img.productimageid,
                'url': img.productImage,
                'order': existingImages.indexOf(img),
              })
          .toList(),
    );
    state.orderedStructure = List.from(state.existingOrderStructure!);
    return state;
  }

  // Display items for UI
  List<ImageDisplayItem> get displayItems => _displayItems;

  // Total image count
  int get totalImageCount => _displayItems.length;

  // Check if has images
  bool get hasImages => totalImageCount > 0;

  // Check if at image limit
  bool hasReachedLimit(int maxImages) => totalImageCount >= maxImages;

  // Add new image
  void addNewImage(XFile image) {
    if (!pendingImages.any((img) => img.path == image.path)) {
      pendingImages.add(image);

      // Add to ordered structure
      orderedStructure.add({
        'type': 'new',
        'id': image.path, // Use path as unique identifier for new images
        'url': image.path,
        'order': orderedStructure.length,
      });

      hasUnsavedChanges = true;
      _updateDisplayItems();
    }
  }

  // Add multiple new images
  void addNewImages(List<XFile> images) {
    for (final image in images) {
      addNewImage(image);
    }
  }

  // Remove image by ID (works for both existing and new)
  void removeImage(String imageId) {
    // Check if it's an existing image
    final existingIndex = existingImages.indexWhere(
      (img) => img.productimageid?.toString() == imageId,
    );

    if (existingIndex != -1) {
      // Mark existing image for deletion
      final imageToDelete = existingImages[existingIndex];
      if (imageToDelete.productimageid != null &&
          !deletedImageIds.contains(imageToDelete.productimageid)) {
        deletedImageIds.add(imageToDelete.productimageid!);
      }
    } else {
      // Remove from pending images
      pendingImages.removeWhere((img) => img.path == imageId);
    }

    // Remove from ordered structure
    orderedStructure.removeWhere((item) => item['id'].toString() == imageId);

    // Update order indices
    _reorderStructure();

    hasUnsavedChanges = true;
    _updateDisplayItems();
  }

  // Reorder images
  void reorderImages(List<String> newOrder) {
    final newOrderedStructure = <Map<String, dynamic>>[];

    for (int i = 0; i < newOrder.length; i++) {
      final imageId = newOrder[i];
      final existingItem = orderedStructure.firstWhere(
        (item) => item['id'].toString() == imageId,
        orElse: () => {},
      );

      if (existingItem.isNotEmpty) {
        existingItem['order'] = i;
        newOrderedStructure.add(existingItem);
      }
    }

    orderedStructure = newOrderedStructure;
    hasUnsavedChanges = true;
    _updateDisplayItems();
  }

  // Get image changes for API calls
  Map<String, dynamic> getImageChanges() {
    return {
      'newImages': pendingImages,
      'deletedImageIds': deletedImageIds,
      'reorderedStructure': orderedStructure,
      'hasChanges': hasUnsavedChanges,
    };
  }

  // Reset to original state
  void resetToOriginal() {
    if (existingOrderStructure != null) {
      orderedStructure = List.from(existingOrderStructure!);
    } else {
      orderedStructure.clear();
    }

    pendingImages.clear();
    deletedImageIds.clear();
    hasUnsavedChanges = false;
    _updateDisplayItems();
  }

  // Clear all images
  void clearAllImages() {
    // Mark all existing images for deletion
    for (final img in existingImages) {
      if (img.productimageid != null &&
          !deletedImageIds.contains(img.productimageid)) {
        deletedImageIds.add(img.productimageid!);
      }
    }

    // Clear pending images
    pendingImages.clear();
    orderedStructure.clear();

    hasUnsavedChanges = true;
    _updateDisplayItems();
  }

  // Update display items based on current state
  void _updateDisplayItems() {
    _displayItems.clear();

    // Sort ordered structure by order
    final sortedStructure = List<Map<String, dynamic>>.from(orderedStructure);
    sortedStructure
        .sort((a, b) => (a['order'] ?? 0).compareTo(b['order'] ?? 0));

    for (final item in sortedStructure) {
      final imageId = item['id'].toString();
      final type = item['type'];

      if (type == 'existing') {
        // Check if not marked for deletion
        final numericId = int.tryParse(imageId);
        if (numericId != null && !deletedImageIds.contains(numericId)) {
          final existingImage = existingImages.firstWhere(
            (img) => img.productimageid == numericId,
            orElse: () => ProdImages(),
          );

          if (existingImage.productImage != null) {
            _displayItems.add(ImageDisplayItem(
              id: imageId,
              displayUrl: existingImage.productImage!,
              isExisting: true,
              existingImage: existingImage,
            ));
          }
        }
      } else if (type == 'new') {
        // Find the pending image
        final pendingImage = pendingImages.firstWhere(
          (img) => img.path == imageId,
          orElse: () => XFile(''),
        );

        if (pendingImage.path.isNotEmpty) {
          _displayItems.add(ImageDisplayItem(
            id: imageId,
            displayUrl: pendingImage.path,
            isExisting: false,
            newImageFile: pendingImage,
          ));
        }
      }
    }
  }

  // Reorder structure indices
  void _reorderStructure() {
    for (int i = 0; i < orderedStructure.length; i++) {
      orderedStructure[i]['order'] = i;
    }
  }

  // Copy with method
  UnifiedProductImageState copyWith({
    List<ProdImages>? existingImages,
    List<XFile>? pendingImages,
    List<int>? deletedImageIds,
    List<Map<String, dynamic>>? orderedStructure,
    List<Map<String, dynamic>>? existingOrderStructure,
    bool? hasUnsavedChanges,
  }) {
    final newState = UnifiedProductImageState(
      existingImages: existingImages ?? this.existingImages,
      pendingImages: pendingImages ?? this.pendingImages,
      deletedImageIds: deletedImageIds ?? this.deletedImageIds,
      orderedStructure: orderedStructure ?? this.orderedStructure,
      existingOrderStructure:
          existingOrderStructure ?? this.existingOrderStructure,
      hasUnsavedChanges: hasUnsavedChanges ?? this.hasUnsavedChanges,
    );
    return newState;
  }
}

/// Display item for unified image handling
class ImageDisplayItem {
  final String id;
  final String displayUrl;
  final bool isExisting;
  final ProdImages? existingImage;
  final XFile? newImageFile;

  ImageDisplayItem({
    required this.id,
    required this.displayUrl,
    required this.isExisting,
    this.existingImage,
    this.newImageFile,
  });

  // Get display widget data
  String get imageUrl => displayUrl;

  bool get isNetworkImage => isExisting && displayUrl.startsWith('http');

  bool get isFileImage => !isExisting;

  // For drag and drop
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ImageDisplayItem &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;
}
